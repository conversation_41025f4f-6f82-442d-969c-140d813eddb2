<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.evertrue</groupId>
    <artifactId>basepom</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <groupId>com.evertrue</groupId>
  <artifactId>contacts_storm</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>jar</packaging>

  <properties>
    <basepom.check.fail-duplicate-finder>false</basepom.check.fail-duplicate-finder>
    <basepom.check.fail-findbugs>false</basepom.check.fail-findbugs>
    <basepom.check.fail-checkstyle>false</basepom.check.fail-checkstyle>
    <basepom.check.fail-dependency-versions-check>false</basepom.check.fail-dependency-versions-check>
    <dep.searchutils.version>2.4-SNAPSHOT</dep.searchutils.version>
    <dep.contacts_client.version>2.0-SNAPSHOT</dep.contacts_client.version>
    <dep.aws.version>1.11.332</dep.aws.version>
    <dep.mysql-connector-java.version>5.1.47</dep.mysql-connector-java.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>json_utils</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>secrets_module</artifactId>
      <version>1.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.storm</groupId>
      <artifactId>storm-core</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.storm</groupId>
      <artifactId>storm-kafka</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.kafka</groupId>
      <artifactId>kafka_2.11</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>exhale</artifactId>
    </dependency>
    <dependency>
      <groupId>com.codahale.metrics</groupId>
      <artifactId>metrics-core</artifactId>
    </dependency>
    <dependency>
      <groupId>net.kencochrane.raven</groupId>
      <artifactId>raven</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>pagerduty_client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>authclient</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>ContactsData</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>contacts_client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sendgrid</groupId>
      <artifactId>sendgrid-java</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.spullara.mustache.java</groupId>
      <artifactId>compiler</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>property_config</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>dna_client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.xerial.snappy</groupId>
      <artifactId>snappy-java</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>exhale</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>searchutils</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>cassandra-utilities</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>ContactsEsUtils</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>webcommon</artifactId>
    </dependency>
    <dependency>
      <groupId>org.coursera</groupId>
      <artifactId>metrics-datadog</artifactId>
    </dependency>
    <dependency>
      <groupId>net.spy</groupId>
      <artifactId>spymemcached</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-dbutils</groupId>
      <artifactId>commons-dbutils</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
    </dependency>
    <dependency>
      <groupId>com.datastax.cassandra</groupId>
      <artifactId>cassandra-driver-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>userdata</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.kafka</groupId>
      <artifactId>kafka-clients</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mchange</groupId>
      <artifactId>c3p0</artifactId>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-sqs</artifactId>
    </dependency>
    <dependency>
      <groupId>org.elasticsearch</groupId>
      <artifactId>elasticsearch</artifactId>
    </dependency>
    <dependency>
      <groupId>com.codahale.metrics</groupId>
      <artifactId>metrics-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>log4j-over-slf4j</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>dropwizard-hibernate-outside</artifactId>
    </dependency>
    <dependency>
      <groupId>io.dropwizard</groupId>
      <artifactId>dropwizard-db</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-yaml</artifactId>
    </dependency>
    <dependency>
      <groupId>javax.validation</groupId>
      <artifactId>validation-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.evertrue</groupId>
      <artifactId>ems_client</artifactId>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-core</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
