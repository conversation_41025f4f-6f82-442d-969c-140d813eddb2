com.et.auth.base_url=https://api.evertrue.com/auth

com.et.sentry.dsn=http://a0dd22ee354e4d83b5b309fe637d69ae:<EMAIL>/7751

com.et.es.hosts=prod-searchcoordinator-v2-contacts-1b,prod-searchcoordinator-v2-contacts-1c,prod-searchcoordinator-v2-contacts-1d
com.et.es.cluster.name=contacts-search-v2
com.et.secrets_manager.secret_name=prod/storm
com.et.contacts.storm.redis.host=prod-sodas-1c.vwbbgm.0001.use1.cache.amazonaws.com
com.et.contacts.hadoop.redis.host=redis-hadoop-1b.vwbbgm.0001.use1.cache.amazonaws.com

com.et.contacts.notifications.geocode.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/prod-contacts-geocode-notify
com.et.contacts.email.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/prod-email
com.et.contacts.suggested_update.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/prod-suggested-update-notify
com.et.contacts.list.change.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/prod-contacts-list-change-notify
com.et.social.notifications.change.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/prod-social-change-notify
com.et.contacts.news_mentions.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/prod-contacts-news-mentions

com.et.kafka.brokers=prod-kafka-1b.priv.evertrue.com:9092,prod-kafka-1c.priv.evertrue.com:9092,prod-kafka-1d.priv.evertrue.com:9092
com.et.contacts.kafka.zookeeper.connect=prod-zookeeper-1b.priv.evertrue.com:2181,prod-zookeeper-1c.priv.evertrue.com:2181,prod-zookeeper-1d.priv.evertrue.com:2181/kafka
com.et.contacts.kafka.topic.contacts_wal=contacts_write_ahead_log
com.et.contacts.kafka.topic.contacts_refresh=contacts_refresh_log
com.et.contacts.kafka.topic.contacts_geocode=contacts_geocode_log
com.et.contacts.kafka.topic.identity_wal=identity_wal
com.et.kafka.topic.gifts_es_wal=gifts_write_es_ahead_log
com.et.kafka.topic.gifts_wal=gifts_write_ahead_log

com.et.contacts.base_url=https://api-hb.evertrue.com/contacts
com.et.memcache.host=prod-storm-1b.vwbbgm.0001.use1.cache.amazonaws.com:11211

com.et.ems.base_url=https://api.evertrue.com/ems
com.et.dna.base_url=https://api-hb.evertrue.com/1.0
com.et.dna.new.base_url=https://api.evertrue.com/dna

com.et.sentry.dsn=http://a0dd22ee354e4d83b5b309fe637d69ae:<EMAIL>/7751

com.et.contacts.cas.hosts=prod-contacts-cass-1b-1,prod-contacts-cass-1b-2,prod-contacts-cass-1c-1,prod-contacts-cass-1c-2,prod-contacts-cass-1d-1,prod-contacts-cass-1d-2
com.et.contacts.cas.contacts_keyspace=contacts
com.et.contacts.cas.contacts_index_keyspace=contacts_index
com.et.contacts.cas.geocodes_keyspace=geocodes

com.et.aws.metadata.url=http://***************/latest/meta-data/instance-id
com.et.mcrouter.us.east.b=prod-contacts-cass-1b-1.priv.evertrue.com:11211:dc1:b,prod-contacts-cass-1b-2.priv.evertrue.com:11211:dc1:b
com.et.mcrouter.us.east.c=prod-contacts-cass-1c-1.priv.evertrue.com:11211:dc1:c,prod-contacts-cass-1c-2.priv.evertrue.com:11211:dc1:c
com.et.mcrouter.us.east.d=prod-contacts-cass-1d-1.priv.evertrue.com:11211:dc1:d,prod-contacts-cass-1d-2.priv.evertrue.com:11211:dc1:d

com.et.dna.redis.host=redis-prod-rg.vwbbgm.ng.0001.use1.cache.amazonaws.com

com.et.search.oid_index_mapping.base_url=https://api-hb.evertrue.com/search/v2/oid_index_mapping/search

com.et.search.contacts_index.name=prod-contacts
com.et.search.contacts_index.prefix=prod
com.et.search.sodas_index.name=prod-sodas
com.et.search.users_index.name=prod-users

com.et.events.base_url=https://api.evertrue.com/events
