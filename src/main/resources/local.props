com.et.auth.base_url=https://stage-api.evertrue.com/auth

com.et.es.hosts=localhost
com.et.es.cluster.name=contacts-search-v2

com.et.contacts.storm.redis.host=localhost
com.et.contacts.hadoop.redis.host=localhost

com.et.contacts.notifications.geocode.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/dev-contacts-geocode-notify
com.et.contacts.email.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/dev-email
com.et.contacts.suggested_update.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/dev-suggested-update-notify
com.et.contacts.list.change.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/dev-contacts-list-change-notify
com.et.social.notifications.change.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/dev-social-change-notify
com.et.contacts.news_mentions.sqs.queue.name=https://sqs.us-east-1.amazonaws.com/037590317780/dev-contacts-news-mentions

com.et.kafka.brokers=localhost:9092
com.et.contacts.kafka.zookeeper.connect=localhost:2181/kafka
com.et.contacts.kafka.topic.contacts_wal=contacts_write_ahead_log
com.et.contacts.kafka.topic.contacts_refresh=contacts_refresh_log
com.et.contacts.kafka.topic.contacts_geocode=contacts_geocode_log
com.et.contacts.kafka.topic.identity_wal=identity_wal
com.et.kafka.topic.gifts_es_wal=gifts_write_es_ahead_log
com.et.kafka.topic.gifts_wal=gifts_write_ahead_log

com.et.contacts.base_url=https://stage-api.evertrue.com/contacts
com.et.memcache.host=localhost:11211

com.et.ems.base_url=https://stage-api.evertrue.com/ems
com.et.dna.base_url=https://stage-api.evertrue.com/1.0
com.et.dna.new.base_url=https://stage-api.evertrue.com/dna

com.et.contacts.cas.hosts=localhost
com.et.contacts.cas.contacts_keyspace=contacts
com.et.contacts.cas.contacts_index_keyspace=contacts_index
com.et.contacts.cas.geocodes_keyspace=geocodes

com.et.aws.metadata.url=http://***************/latest/meta-data/instance-id
com.et.mcrouter.us.east.b=mc-api-stage-1b.vwbbgm.0001.use1.cache.amazonaws.com:11211:dc1:b
com.et.mcrouter.us.east.c=mc-api-stage-1b.vwbbgm.0001.use1.cache.amazonaws.com:11211:dc1:c
com.et.mcrouter.us.east.d=mc-api-stage-1b.vwbbgm.0001.use1.cache.amazonaws.com:11211:dc1:d

com.et.dna.redis.host=redis-stage-rg.vwbbgm.ng.0001.use1.cache.amazonaws.com

com.et.search.oid_index_mapping.base_url=http://localhost:8080/search/v2/oid_index_mapping/search

com.et.search.contacts_index.name=local-contacts
com.et.search.contacts_index.prefix=local
com.et.search.sodas_index.name=local-sodas
com.et.search.users_index.name=evertrue

com.et.events.base_url=https://stage-api.evertrue.com/events
com.et.secrets_manager.secret_name=local/storm
