package com.et.contacts.storm.bolt;

import java.util.Map;

import org.apache.log4j.Logger;

import com.datastax.driver.core.PreparedStatement;
import com.datastax.driver.core.Session;
import com.et.cassandra.utils.CassandraWriteLock;
import com.et.cassandra.utils.MySQLToCassWrite;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactChangeEvent;
import com.et.contacts.v1.data.NestedPropertyMap;
import com.et.contacts.v1.data.ProtoUtils;
import com.google.common.cache.LoadingCache;
import com.google.inject.TypeLiteral;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;

public class CassandraSyncBolt extends BaseBasicBolt {
	private static final Logger log = Logger.getLogger(CassandraSyncBolt.class);

	private Session contactsKsSession;
	private Session contactsIndexKsSession;
	private PreparedStatement selectContactMetaRow;

	private CassandraWriteLock cassandraLock;
	private LoadingCache<Integer, NestedPropertyMap> orgPropCache;
	private MySQLToCassWrite casWrite;

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);

		contactsKsSession = GuiceSingleton.get().getInstanceWithName(Session.class, "ContactsKsSession");
		contactsIndexKsSession = GuiceSingleton.get().getInstanceWithName(Session.class, "ContactsIndexKsSession");
		cassandraLock = GuiceSingleton.get().getInstance(CassandraWriteLock.class);
		selectContactMetaRow = GuiceSingleton.get().getInstanceWithName(PreparedStatement.class, "SelectContactMetaRow");

		this.orgPropCache = GuiceSingleton.get().getInstanceWithName(
				new TypeLiteral<LoadingCache<Integer, NestedPropertyMap>>() {
				}, "ContactsPropMapCache");

		casWrite = new MySQLToCassWrite(contactsKsSession, contactsIndexKsSession);
	}

	@Override
	public void execute(Tuple input, BasicOutputCollector collector) {
		ContactChangeEvent event = (ContactChangeEvent) input.getValueByField("event");
		Contact contact = event.getCurrent();

		// strip meta fields, this proto should be used for writing only
		Contact strippedContact = ProtoUtils.stripFieldsAndSetMeta(contact);

		try {
			if (!cassandraLock.getContactLockWithRetries(contact.getId(), 20)) {
				throw new ReportedFailedException("Couldn't acquire cassandra lock for contactId: " + contact.getId());
			}

			if (event.getType() == ContactChangeEvent.Type.DELETE) {
				casWrite.deleteContactIndexData(contact);
			} else if (event.getType() == ContactChangeEvent.Type.REFRESH) {
				casWrite.writeToOidIndex(strippedContact, true);
			} else {
				if (!contact.getDeleted()) {
					casWrite.writeToOidIndex(strippedContact);
				}
			}
		} catch (Exception e) {
			log.error(e, e);
			throw new ReportedFailedException(e);
		} finally {
			cassandraLock.returnContactLock(contact.getId());
		}

		collector.emit(new Values(input.getLongByField("contact_id"), event));
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
        declarer.declare(new Fields("contact_id", "event"));
	}
}
