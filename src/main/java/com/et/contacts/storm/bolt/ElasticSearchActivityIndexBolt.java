package com.et.contacts.storm.bolt;

import java.util.Map;

import org.apache.log4j.Logger;

import com.et.contacts.es.indexers.ElasticSearchContactActivityIndexer;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactChangeEvent;
import com.et.contacts.v1.data.NestedPropertyMap;
import com.google.common.cache.LoadingCache;
import com.google.inject.TypeLiteral;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;

public class ElasticSearchActivityIndexBolt extends BaseBasicBolt {

    private final static Logger LOG = Logger.getLogger(ElasticSearchActivityIndexBolt.class);

    private LoadingCache<Integer, NestedPropertyMap> orgPropCache;
    private ElasticSearchContactActivityIndexer esContactActivityIndexer;

    @Override
    public void prepare(Map stormConf, TopologyContext context) {
        super.prepare(stormConf, context);
        orgPropCache = GuiceSingleton.get().getInstanceWithName(
                new TypeLiteral<LoadingCache<Integer, NestedPropertyMap>>() {
                }, "ContactsPropMapCache");
        esContactActivityIndexer = GuiceSingleton.get().getInstance(ElasticSearchContactActivityIndexer.class);
    }

    @Override
    public void execute(Tuple input, BasicOutputCollector collector) {
        try {
            ContactChangeEvent event = (ContactChangeEvent) input.getValueByField("event");

            NestedPropertyMap nestedPropMap = orgPropCache.get(event.getOid());
            esContactActivityIndexer.indexToElasticSearchIfNecessary(event, nestedPropMap, true);

            collector.emit(new Values(input.getLongByField("contact_id"), event));
        } catch (Exception e) {
            LOG.error(e, e);
            throw new ReportedFailedException(e);
        }

    }

    @Override
    public void declareOutputFields(OutputFieldsDeclarer declarer) {
        declarer.declare(new Fields("contact_id", "event"));
    }

}
