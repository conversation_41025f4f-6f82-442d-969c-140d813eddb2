package com.et.contacts.storm.bolt;

import java.util.Map;

import org.apache.log4j.Logger;

import com.et.contacts.client.ContactsClient;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactChangeEvent;
import com.et.contacts.v1.data.ContactsProtoBuf.UpdateSource;
import com.et.webcommon.auth.AuthApiCreds;
import com.google.inject.TypeLiteral;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;
import static com.et.secrets.SecretsModule.SECRETS_MANAGER_SECRET;

@SuppressWarnings("serial")
public class GiftDeleteBolt extends BaseBasicBolt {
	private final static Logger log = Logger.getLogger(GiftDeleteBolt.class);
	private Map<String, String> secrets;
	private ContactsClient contactsClient;
	private AuthApiCreds authApiCreds;

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);
		this.contactsClient = GuiceSingleton.get().getInstance(ContactsClient.class);

		TypeLiteral<Map<String, String>> tl = new TypeLiteral<Map<String, String>>() {};
		this.secrets = GuiceSingleton.get().getInstanceWithName(tl, SECRETS_MANAGER_SECRET);

		this.authApiCreds = AuthApiCreds.newAppCreds(secrets);
	}

	@Override
	public void execute(Tuple input, BasicOutputCollector collector) {
		try {
			ContactChangeEvent event = (ContactChangeEvent) input.getValueByField("event");
			Contact contact = event.getCurrent();

			if (isDeleteEvent(event, contact)) {
			    contactsClient.deleteGiftsByContactIdWithRawResponse(contact.getOid(), contact.getId(), authApiCreds, UpdateSource.CSV_IMPORTER);
			}

			collector.emit(new Values(input.getLongByField("contact_id"), event));
		} catch (Exception e) {
			log.error(e, e);
			throw new ReportedFailedException(e);
		}
	}

	private boolean isDeleteEvent(ContactChangeEvent event, Contact currentContact) {
		return event.getType() == ContactChangeEvent.Type.DELETE || currentContact.getDeleted();
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields("contact_id", "event"));
	}
}
