package com.et.contacts.storm.bolt;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;
import com.et.contacts.client.ContactsClient;
import com.et.contacts.client.ContactsServiceException;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.v1.api.gift.cassandra.GiftWal;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactChangeEvent;
import com.et.contacts.v1.data.ContactsProtoBuf.Identity;
import com.et.contacts.v1.data.ContactsProtoBuf.Identity.Type;
import com.et.contacts.v2.data.gifts.GiftPaginated;
import com.et.contacts.v2.data.gifts.hibernate.entity.GiftTransactionEntity;
import com.et.exhale.http.HttpResponse;
import com.et.search.client.transportclient.ETTransportClient;
import com.et.webcommon.auth.AuthApiCreds;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.TypeLiteral;
import com.google.protobuf.AbstractMessage;
import org.apache.log4j.Logger;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import static com.et.secrets.SecretsModule.SECRETS_MANAGER_SECRET;

public class ElasticSearchGiftBolt extends BaseBasicBolt {
    private static final Logger LOG = Logger.getLogger(ElasticSearchGiftBolt.class);
    private static final int SCROLL_TIME = 60000;
    private static final String ES_TYPE = "gift";
    private ETTransportClient etTransportClient;
    private String indexName;
    private GiftWal giftWal;
    private Map<String, String> secrets;
    private AuthApiCreds authApiCreds;
    private ContactsClient contactsClient;
    private ObjectMapper objectMapper;

    @Override
    public void prepare(Map stormConf, TopologyContext context) {
        super.prepare(stormConf, context);
        etTransportClient = GuiceSingleton.get().getInstance(ETTransportClient.class);
        giftWal = GuiceSingleton.get().getInstance(GiftWal.class);
        this.indexName = GuiceSingleton.get().getInstanceWithName(String.class, "com.et.search.contacts_index.name");
        TypeLiteral<Map<String, String>> tl = new TypeLiteral<Map<String, String>>() {
        };
        this.secrets = GuiceSingleton.get().getInstanceWithName(tl, SECRETS_MANAGER_SECRET);
        this.authApiCreds = AuthApiCreds.newAppCreds(secrets);
        this.contactsClient = GuiceSingleton.get().getInstance(ContactsClient.class);
        this.objectMapper = GuiceSingleton.get().getInstance(ObjectMapper.class);
    }

    @Override
    public void execute(Tuple input, BasicOutputCollector collector) {
        ContactChangeEvent event = (ContactChangeEvent) input.getValueByField("event");
        LOG.info("Starting " + this.getClass().getSimpleName() + " eventType: " + event.getType().name());
        Contact currentContact = event.getCurrent();
        Contact previousContact = event.getPrevious();
        if (Objects.nonNull(currentContact)) {
            LOG.debug("currentContact: " + currentContact.getId());
        }

        if (Objects.nonNull(previousContact)) {
            LOG.debug("previousContact: " + previousContact.getId());
        }

        //contact remote id change.
        Optional<Identity> currentRemoteId =
                currentContact.getIdentityList().stream().filter(it -> it.getType() == Type.REMOTE_ID).findFirst();

        Optional<Identity> previousRemoteId =
                previousContact.getIdentityList().stream().filter(it -> it.getType() == Type.REMOTE_ID).findFirst();

        String valCurRI = currentRemoteId.map(AbstractMessage::toString).orElse(null);
        String valPrevRI = previousRemoteId.map(AbstractMessage::toString).orElse(null);

        LOG.debug("currentRemoteId " + valCurRI + " previousRemoteId " + valPrevRI);

        if (event.getType().equals(ContactChangeEvent.Type.UPDATE)) {
            LOG.info("UPDATED contact. ContactId: " + currentContact.getId());

            if (!currentRemoteId.isPresent() && !previousRemoteId.isPresent()) {
                LOG.error("No remote contact ID present for Update for both version of contact. contactId: " +
                        currentContact.getId());
                collector.emit(new Values(input.getLongByField("contact_id"), event));
                return;
            } else if (currentRemoteId.isPresent() && !previousRemoteId.isPresent()) {
                //same functionality as a CREATE
                createGiftsInEsFromDB(currentContact);
            } else if (!currentRemoteId.isPresent() && previousRemoteId.isPresent()) {
                //same functionality as a DELETE
                changeGiftsInES(previousContact, ContactChangeEvent.Type.DELETE);
            } else if (!currentRemoteId.get().getValue().equals(previousRemoteId.get().getValue())) {
                //contact remote id is updated. delete gifts, search for gifts under the new contact remote id and
                // create in ES.
                changeGiftsInES(previousContact, event.getType());
                createGiftsInEsFromDB(currentContact);
            }
        } else if (isDeleteEvent(event, currentContact)) {
            //contact is deleted reassign gifts off the contact in ES
            LOG.info("DELETED contact " + currentContact.getId());
            changeGiftsInES(currentContact, event.getType());
        } else if (event.getType().equals(ContactChangeEvent.Type.CREATE) && currentRemoteId.isPresent()) {
            //contact is created also create gifts in ES.
            LOG.info("CREATED contact" + currentContact.getId() + " remoteID " + currentRemoteId.get().getValue());
            createGiftsInEsFromDB(currentContact);
        }
        collector.emit(new Values(input.getLongByField("contact_id"), event));
    }

    /**
     * based on the contactChangeEventType all gifts are collected from contact in ES
     * if Update event then the gifts are deleted from ES.
     * if Delete event the gifts are reassigned to null contact in ES.
     *
     * @param previousContact
     * @param contactChangeEventType
     */
    private void changeGiftsInES(Contact previousContact, ContactChangeEvent.Type contactChangeEventType) {
        //get all gifts associated with the previous contact in ES
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("contact_oid", previousContact.getOid()))
                .must(QueryBuilders.termQuery("contact_id", previousContact.getId()));
        SearchResponse searchResponse = getSearchResponseWithScroll(queryBuilder, previousContact.getOid());

        LOG.debug("Total Number of gifts found for contact: " + previousContact.getId() + " #: " +
                searchResponse.getHits().getHits().length);

        while (searchResponse.getHits().getHits().length != 0) {
            for (SearchHit hits : searchResponse.getHits().getHits()) {
                if (hits.isSourceEmpty()) {
                    continue;
                }

                String type = (String) hits.getSource().get("type");
                String remoteId = (String) hits.getSource().get("remote_id");
                try {
                    //need to use the entity from DB since this is the only place that the ContactRemoteId Exist.
                    GiftTransactionEntity entityFromDB = getGiftFromDB(previousContact, type, remoteId);

                    LOG.debug("gift from DB to delete or reassign: " + entityFromDB.toString());
                    //delete message or reassign gifts

                    if (contactChangeEventType.equals(ContactChangeEvent.Type.DELETE)) {
                        LOG.debug("creating RemoveContactFromGift message for ES");
                        giftWal.writeWalRemoveContactFromGiftEntryESOnly(entityFromDB);
                    } else if (contactChangeEventType.equals(ContactChangeEvent.Type.UPDATE)) {
                        LOG.debug("creating delete gift message for ES");
                        giftWal.writeWalDeleteEntryESOnly(com.google.common.base.Optional.absent(),
                                com.google.common.base.Optional.absent(), entityFromDB);
                    }

                } catch (ContactsServiceException e) {
                    if (e.getStatusCode() == 404) {
                        continue;
                    } else {
                        LOG.error(e, e);
                        throw new ReportedFailedException(e);
                    }

                } catch (Exception e) {
                    LOG.error(e, e);
                    throw new ReportedFailedException(e);
                }
            }

            //get the next scroll of gifts to delete
            SearchScrollRequest searchScrollRequest = new SearchScrollRequest(searchResponse.getScrollId())
                    .scroll(new TimeValue(SCROLL_TIME));
            searchResponse = etTransportClient.executeSearchScrollRequest(searchScrollRequest).actionGet();
            if (searchResponse.getHits().getTotalHits() == 0) {
                break;
            }
        }
    }

    /**
     * Calls the contact api to lookup the gift with the UNIQUE constraints listed below.
     *
     * @param contact
     * @param type
     * @param remoteId
     * @return
     * @throws ExecutionException
     * @throws IOException
     */
    private GiftTransactionEntity getGiftFromDB(Contact contact, String type, String remoteId) throws
            ExecutionException, IOException {
        HttpResponse resp = contactsClient.getGiftByRemoteIdWithRawResponse
                (contact.getOid(), authApiCreds, type, remoteId);
        if (resp.isError()) {
            throw new ContactsServiceException(resp.getStatusCode(), "Error fetching gift by remote id " +
                    "for oid: " + contact.getOid()
                    + ", remote_id: " + remoteId + ", gift type : " + type.toString() + " status code: "
                    + resp.getStatusCode());
        }
        return objectMapper.readValue(resp.getResponseAsString(),
                GiftTransactionEntity.class);
    }

    private boolean isDeleteEvent(ContactChangeEvent event, Contact currentContact) {
        return event.getType() == ContactChangeEvent.Type.DELETE || currentContact.getDeleted();
    }

    /**
     * looks up all gifts on contact in DB and creates the gifts in elastic search.
     *
     * @param currentContact
     */
    private void createGiftsInEsFromDB(Contact currentContact) {
        try {
            int limit = 1000;
            int offset = 0;

            while (true) {
                GiftPaginated response = contactsClient.getPaginatedGiftsByContactId(currentContact.getOid(),
                        authApiCreds, currentContact.getId(), limit, offset);

                if (response.getItems().isEmpty()) {
                    return;
                }

                offset = offset + response.getItems().size();
                LOG.info("Gifts on current Contact: " + currentContact.getId() + " gifts: " +
                        response.getItems().size());

                //create gifts documents in elastic search.
                for (GiftTransactionEntity gift : response.getItems()) {
                    LOG.debug("Creating write message for gift: " + gift.toString());
                    giftWal.writeWalCreateEntryESOnly(com.google.common.base.Optional.absent(),
                            com.google.common.base.Optional.absent(), gift);
                }
            }

        } catch (ContactsServiceException e) {
            if (e.getStatusCode() == 404) {
                // contact is already deleted
                changeGiftsInES(currentContact, ContactChangeEvent.Type.DELETE);

            } else {
                LOG.error(e, e);
                throw new ReportedFailedException(e);
            }

        } catch (Exception e) {
            LOG.error(e, e);
            throw new ReportedFailedException(e);
        }

    }

    private SearchResponse getSearchResponseWithScroll(BoolQueryBuilder queryBuilder, int oid) {
        SearchRequest searchRequest = new SearchRequest()
                .indices(indexName).types(ES_TYPE);

        SearchSourceBuilder searchSourceBuilder = SearchSourceBuilder.searchSource()
                .query(queryBuilder);
        SearchRequest scrollRequest = searchRequest.source(searchSourceBuilder).scroll(new TimeValue(SCROLL_TIME));

        return etTransportClient
                .executeSearchRequest(scrollRequest, com.google.common.base.Optional.of(String.valueOf(oid)))
                .actionGet();
    }

    @Override
    public void declareOutputFields(OutputFieldsDeclarer declarer) {

    }
}
