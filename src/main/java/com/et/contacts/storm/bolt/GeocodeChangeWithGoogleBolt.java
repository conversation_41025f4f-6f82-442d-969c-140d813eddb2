package com.et.contacts.storm.bolt;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.codahale.metrics.Timer.Context;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import com.datastax.driver.core.ConsistencyLevel;
import com.datastax.driver.core.PreparedStatement;
import com.datastax.driver.core.Row;
import com.datastax.driver.core.Session;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.storm.utils.CensusUtils;
import com.et.contacts.storm.utils.GeocodeUtils;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObject;
import com.et.contacts.v1.data.PropertyUtils;
import com.et.exhale.http.HttpClient;
import com.et.exhale.http.HttpRequest;
import com.et.exhale.http.HttpRequest.RequestType;
import com.et.exhale.http.HttpResponse;
import com.et.json.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Optional;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.google.common.net.PercentEscaper;
import com.google.common.util.concurrent.RateLimiter;
import com.google.inject.TypeLiteral;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.FailedException;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;
import net.spy.memcached.MemcachedClient;
import static com.et.secrets.SecretsModule.SECRETS_MANAGER_SECRET;

public class GeocodeChangeWithGoogleBolt extends BaseBasicBolt {
	private final static Logger LOG = Logger.getLogger(GeocodeChangeWithGoogleBolt.class);

	private HttpClient httpClient;
	private final static Set<String> RETRY_STATUSES = ImmutableSet.of("OVER_QUERY_LIMIT", "REQUEST_DENIED");
	private final static RateLimiter rateLimiter = RateLimiter.create(200_000 / 24 / 3600);
	private Map<String, String> secrets;
	private String newApiKey;
	private String hostUri;
	private Session geocodeKsSession;
	private PreparedStatement selectFromCacheStmt;
	private PreparedStatement insertIntoCacheStmt;
	private GeocodeUtils geocodeUtils;
	private CensusUtils censusUtils;
	private PercentEscaper urlEscaper;
	private Timer censusTimer;

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);

		TypeLiteral<Map<String, String>> tl = new TypeLiteral<Map<String, String>>() {};
		this.secrets = GuiceSingleton.get().getInstanceWithName(tl, SECRETS_MANAGER_SECRET);

		this.newApiKey = secrets.get("com.et.geo.api.google.new_key");
		this.hostUri = secrets.get("com.et.geo.api.google.host");

		this.geocodeKsSession = GuiceSingleton.get().getInstanceWithName(Session.class, "GeocodesKsSession");
		selectFromCacheStmt = geocodeKsSession
				.prepare("SELECT addr_md5, addr_str, payload, WRITETIME(payload) AS writetime FROM cache WHERE addr_md5 = ?")
				.setConsistencyLevel(
						ConsistencyLevel.ONE);
		this.insertIntoCacheStmt = geocodeKsSession.prepare("INSERT INTO cache (addr_md5, addr_str, payload) VALUES (?,?,?)").setConsistencyLevel(ConsistencyLevel.ONE);
		this.httpClient = GuiceSingleton.get().getInstance(HttpClient.class);
		this.geocodeUtils = new GeocodeUtils();
		this.censusUtils = new CensusUtils();
		this.urlEscaper = new PercentEscaper("-_.~", false);
		MetricRegistry registry = GuiceSingleton.get().getInstance(MetricRegistry.class);
		censusTimer = registry.timer(getClass().getCanonicalName() + ".census_lookup_time");
	}

	@Override
	public void execute(Tuple input, BasicOutputCollector collector) {
		Contact contactWithAddressesToChangeGeocode = (Contact) input.getValueByField("contact-with-addresses-to-change-geocode");

		//skip contacts that are deleted, they shouldn't be in the queue
		if (contactWithAddressesToChangeGeocode.getDeleted()) return;

		Contact.Builder contactWithChangedGeocodeBldr = Contact.newBuilder(contactWithAddressesToChangeGeocode);

		Optional<Integer> maybeAddressesIndex = PropertyUtils.getNvolistIndexByName (contactWithChangedGeocodeBldr, "addresses");
		if (!maybeAddressesIndex.isPresent ()) {
			return;
	    }

		for (int i = 0; i < contactWithChangedGeocodeBldr.getNameValueObjectListBuilder(maybeAddressesIndex.get()).getNameValueObjectCount(); i++) {
			NameValueObject.Builder addressBldr = contactWithChangedGeocodeBldr
					.getNameValueObjectListBuilder(maybeAddressesIndex.get()).getNameValueObjectBuilder(i);

			String addressString = geocodeUtils.getAddressString(addressBldr.build(), false);
	        if (Strings.isNullOrEmpty(addressString)) {
	        	contactWithChangedGeocodeBldr.getNameValueObjectListBuilder(maybeAddressesIndex.get()).removeNameValueObject(i);
	        	i--;
				continue;
			}

	        List<Row> existingGeocodes = geocodeUtils.checkGeocodeCache(geocodeKsSession, selectFromCacheStmt, addressString);
	        if(existingGeocodes.isEmpty()) {
	        	Map<String, Double> geocodedAddress = geocodeAddressFromExternalSource(addressString, contactWithAddressesToChangeGeocode.getId());

	        	// If not geocode could be resolved, skip
	        	if(geocodedAddress.isEmpty()) {
	        		contactWithChangedGeocodeBldr.getNameValueObjectListBuilder(maybeAddressesIndex.get()).removeNameValueObject(i);
	        		i--;
	        		continue;
	        	}

				Context time = censusTimer.time();
				geocodeUtils.addGeocodedAndCensusDataToAddress(addressBldr, geocodedAddress, censusUtils);
				time.stop();

	        } else {
				Row existingGeocode = existingGeocodes.get(0);
				JsonNode cachedPayload = JsonUtils.fromString(existingGeocode.getString("payload"));
	        	Map<String, Double> geocodedAddress = geocodeUtils.getGeocodeFromPayload(cachedPayload);

				if(geocodedAddress.isEmpty()) {
					contactWithChangedGeocodeBldr.getNameValueObjectListBuilder(maybeAddressesIndex.get()).removeNameValueObject(i);
					i--;
					continue;
				}

				geocodeUtils.addGeocodedAndCensusDataToAddress(addressBldr, geocodedAddress, censusUtils);
	        }
		}

		Contact contactWithChangedGeocode = contactWithChangedGeocodeBldr.build();

		if (contactWithChangedGeocode.getNameValueObjectList(0).getNameValueObjectCount() > 0) {
			collector.emit(new Values(contactWithChangedGeocode.getId(), contactWithChangedGeocode));
		}
	}

	private Map<String, Double> geocodeAddressFromExternalSource(String addressString, long contactId) {

		String encodedAddress = urlEscaper.escape(addressString);

		try {
			HttpRequest req = HttpRequest.newBuilder(RequestType.GET,
					hostUri + "/maps/api/geocode/json?key=" + newApiKey + "&address=" + encodedAddress).build();
			rateLimiter.acquire();
			HttpResponse resp = httpClient.execute(req);

			if (resp.isSuccess()) {
				String respBody = resp.getResponseAsString();
				JsonNode json;
				try {
					json = JsonUtils.fromString(respBody);
				} catch (Exception e) {
					throw new ReportedFailedException("Error parsing response with request url: "
							+ req.prettyPrintUriAndParams() + ", body: " + respBody);
				}
				String respStatus = json.get("status").asText();

				if (respStatus.equals("OK")) {
					cachePayload(addressString, json);
					return geocodeUtils.getGeocodeFromPayload(json);
				} else if (respStatus.equals("ZERO_RESULTS")) {
					cachePayload(addressString, json);

				} else if (RETRY_STATUSES.contains(respStatus)) {
					throw new ReportedFailedException("Error geocoding address: " + addressString + ", status: " + respStatus);
				}

			} else if (resp.getStatusCode() == 400) {
				LOG.error("bad geocode request for contact id: " + contactId);

			} else if (resp.getStatusCode() == 403) {
				String respBody = resp.getResponseAsString();
				throw new ReportedFailedException("Error geocoding address: " + addressString + ", status code: " + resp.getStatusCode() + ", "+ respBody);

			} else if (resp.getStatusCode() == 413 || resp.getStatusCode() == 414) {
				LOG.error("address content too long");

			} else {
                String respBody = resp.getResponseAsString();

                // only retry if it's not an unknown_error, these don't seem to resolve themselves after repeated retries
                if(!respBody.contains("UNKNOWN_ERROR")) {
                	throw new ReportedFailedException("Error geocoding address: " + addressString + ", status code: " + resp.getStatusCode() + ", "+ respBody);
                }
			}
		} catch (FailedException e) {
			throw e;
		} catch (Exception e) {
			throw new ReportedFailedException(e);
		}

		// return empty map if google could not resolve the address to a geocode
		return Maps.newHashMap();
	}

	private void cachePayload(String addressString, JsonNode json) {
		try {
			geocodeKsSession
					.execute(insertIntoCacheStmt.bind(geocodeUtils.getCacheKey(addressString), addressString, json.toString()));
		} catch (Exception e) {
			LOG.error("Cache write to Cassandra failed", e);
		}
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields("contact_id", "contact-to-update"));
	}
}
