package com.et.contacts.storm.bolt;

import java.util.List;
import java.util.Map;

import com.et.contacts.v1.data.ContactsProtoBuf;
import com.et.contacts.v1.data.ProtoWithListIndex;
import com.google.common.collect.Lists;
import org.apache.log4j.Logger;

import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.storm.utils.CensusUtils;
import com.et.contacts.storm.utils.GeocodeUtils;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactChangeEvent;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObject;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObjectList;
import com.et.contacts.v1.data.ContactsProtoBuf.UpdateSource;
import com.et.contacts.v1.data.PropertyUtils;
import com.et.contacts.v1.data.property.NVPWithOptionalParent;
import com.et.contacts.v1.data.wrapper.ContactDelta;
import com.et.contacts.v1.data.wrapper.ContactWrapper;
import com.google.common.base.Optional;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;

public class GeocodePrivatizeBolt extends BaseBasicBolt {
	private final static Logger LOG = Logger.getLogger(GeocodePrivatizeBolt.class);
	private GeocodeUtils geocodeUtils;
	private CensusUtils  censusUtils;

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);
		geocodeUtils = GuiceSingleton.get().getInstance(GeocodeUtils.class);
		censusUtils  = GuiceSingleton.get ().getInstance(CensusUtils.class);
	}

	@Override
	public void execute(Tuple input, BasicOutputCollector collector) {
		ContactChangeEvent event = (ContactChangeEvent) input.getValueByField("event");
		Contact currentContact = event.getCurrent();

		Optional<ProtoWithListIndex<NameValueObjectList>> maybeAddresses = PropertyUtils
				.getNvolistByName(currentContact, "addresses");

		if (!maybeAddresses.isPresent()) {
			return;
		}

		NameValueObjectList updatedAddresses = getAddressesWithUpdatedGeocodeProperties(maybeAddresses.get().getProto());

		if (updatedAddresses.getNameValueObjectCount() == 0) {
			return;
		}

		Contact contactWithPrivatizedGeocode = currentContact
				.toBuilder()
				.clearIdentity()
				.clearRoleId()
				.clearNameValuePair()
				.clearNameValueObject()
				.clearNameValueObjectList()
				.addNameValueObjectList(updatedAddresses)
				.build();

		collector.emit(new Values(contactWithPrivatizedGeocode.getId(), contactWithPrivatizedGeocode));
	}

	private NameValueObjectList getAddressesWithUpdatedGeocodeProperties(NameValueObjectList addresses) {
		NameValueObjectList.Builder addressesToUpdateBldr = addresses.toBuilder().clearNameValueObject();

		for (NameValueObject address : addresses.getNameValueObjectList()) {
			NameValueObject.Builder addrBldr = address.toBuilder().clearNameValuePair();
			boolean addressIsPrivate = false;
			UpdateSource privatizedSource = UpdateSource.HADOOP;
			for (ContactsProtoBuf.NameValuePair nvp : address.getNameValuePairList()) {
				if (!geocodeUtils.isGeocodeProperty(nvp.getProperty().getName()) && nvp.getPrivate()) {
					addressIsPrivate = true;
					privatizedSource = nvp.getPrivatizedSource();
				}
			}

			for (ContactsProtoBuf.NameValuePair nvp : address.getNameValuePairList()) {
				if (geocodeUtils.isGeocodeProperty(nvp.getProperty().getName())) {
					if (nvp.getPrivate() != addressIsPrivate) {
						addrBldr.addNameValuePair(nvp.toBuilder().setPrivate(addressIsPrivate).setUpdateSource(privatizedSource));
					}
				}
			}

			if (addrBldr.getNameValuePairCount() > 0) {
				addressesToUpdateBldr.addNameValueObject(addrBldr);
			}
		}

		return addressesToUpdateBldr.build();
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields("contact_id", "contact-to-update"));
	}
}
