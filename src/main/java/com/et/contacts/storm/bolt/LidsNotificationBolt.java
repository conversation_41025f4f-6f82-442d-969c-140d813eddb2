package com.et.contacts.storm.bolt;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactChangeEvent;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactChangeEvent.Type;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObject;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObjectList;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValuePair;
import com.google.common.collect.Lists;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;

@SuppressWarnings("serial")
public class LidsNotificationBolt extends BaseBasicBolt {
	private final static Logger log = Logger.getLogger(LidsNotificationBolt.class);
	private final static String INSERT_INTO_WARMING_QUEUE_SQL = "INSERT INTO warming_queue (oid, contact_id, created_at, updated_at) VALUES (?,?,?,?) ON DUPLICATE KEY UPDATE `updated_at`=?";

	private Connection batchConn;

	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields("contact_id", "event"));
	}

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);
		this.batchConn = GuiceSingleton.get().getInstanceWithName(Connection.class, "SodasBatchConn");
	}

	public void execute(Tuple tuple, BasicOutputCollector collector) {
		ContactChangeEvent event = (ContactChangeEvent) tuple.getValueByField("event");
		Contact currContact = event.getCurrent();
		Contact prevContact = event.getPrevious();

		try {
			if (event.getType() == Type.CREATE) {
				insertContactIntoLiWarmingQueue(currContact.getOid(), currContact.getId());
			} else if (event.getType() == Type.UPDATE) {
			    List<String> currContactEmails = getContactEmails(currContact);
			    List<String> prevContactEmails =  getContactEmails(prevContact);

			    List<String> newEmails = getNewlyAddedEmails(currContactEmails, prevContactEmails);

			    if (newEmails.size() > 0) {
			        insertContactIntoLiWarmingQueue(currContact.getOid(), currContact.getId());
			    }
			}
		} catch (Exception e) {
			throw new ReportedFailedException(e);
		}

		collector.emit(new Values(tuple.getLongByField("contact_id"), event));
	}

	private void insertContactIntoLiWarmingQueue(int oid, long contactId) {
	    try {
	        PreparedStatement ps = batchConn 
	                .prepareStatement(INSERT_INTO_WARMING_QUEUE_SQL);
	        Long now = System.currentTimeMillis();

	        ps.setInt(1, oid);
	        ps.setLong(2, contactId);
	        ps.setTimestamp(3, new java.sql.Timestamp(now));
	        ps.setTimestamp(4, new java.sql.Timestamp(now));
	        ps.setTimestamp(5, new java.sql.Timestamp(now));

	        ps.addBatch();

	        ps.executeBatch();
	    } catch (SQLException e1) {
	        e1.printStackTrace();
	    }
	}

	private List<String> getContactEmails(Contact contact) {
		for (NameValueObjectList nvol : contact.getNameValueObjectListList()) {
			if (nvol.getProperty().getName().equals("emails")) {
				List<String> emails = Lists.newArrayList();

				for (NameValueObject nvo : nvol.getNameValueObjectList()) {
					for (NameValuePair nvp : nvo.getNameValuePairList()) {
						if (nvp.getProperty().getName().equals("email")) {
							emails.add(nvp.getValue());
							break;
						}
					}
				}

				return emails;
			}
		}

		return Lists.newArrayList();
	}

	private List<String> getNewlyAddedEmails(List<String> currContactEmails, List<String> prevContactEmails) {
	    List<String> newEmails = Lists.newArrayList();

	    for (String newEmail : currContactEmails) {
	        Boolean emailAlreadyExists = false;

	        for (String oldEmail : prevContactEmails) {
	            emailAlreadyExists = emailAlreadyExists || (newEmail.equals(oldEmail));
	        }

	        if (!emailAlreadyExists) {
	            newEmails.add(newEmail);
	        }
	    }

	    return newEmails;
	}
}