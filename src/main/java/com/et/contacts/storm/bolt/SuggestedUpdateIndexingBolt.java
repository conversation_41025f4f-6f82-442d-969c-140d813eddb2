package com.et.contacts.storm.bolt;

import java.nio.charset.StandardCharsets;
import java.util.Map;

import org.apache.log4j.Logger;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;

import com.amazonaws.services.sqs.AmazonSQSClient;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.v1.data.ContactsProtoBuf.SuggestedUpdate;
import com.et.contacts.v1.data.transcoder.SuggestedUpdateTranscoder;
import com.et.dna.client.DNAClient;
import com.et.json.JsonBuilder;
import com.et.search.client.transportclient.ETTransportClient;
import com.et.webcommon.auth.AuthApiCreds;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Splitter;
import com.google.common.collect.Iterables;
import com.google.common.hash.Hashing;
import com.google.inject.TypeLiteral;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;
import static com.et.secrets.SecretsModule.SECRETS_MANAGER_SECRET;

public class SuggestedUpdateIndexingBolt extends BaseBasicBolt {
	private static final String EMAIL_NAME = "contacts.suggested_update";
	private final static Logger log = Logger.getLogger(SuggestedUpdateIndexingBolt.class);
	private Map<String, String> secrets;
	private ETTransportClient etTransportClient;
	private DNAClient dnaClient;
	private AmazonSQSClient sqsClient;
	private SuggestedUpdateTranscoder suTranscoder;
	private String sqsEmailQueue;
	private String contactsIndexName;

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);

		TypeLiteral<Map<String, String>> tl = new TypeLiteral<Map<String, String>>() {};
		this.secrets = GuiceSingleton.get().getInstanceWithName(tl, SECRETS_MANAGER_SECRET);

		this.dnaClient = GuiceSingleton.get().getInstance(DNAClient.class);
		this.etTransportClient = GuiceSingleton.get().getInstance(ETTransportClient.class);
		this.sqsClient = GuiceSingleton.get().getInstance(AmazonSQSClient.class);
		this.suTranscoder = GuiceSingleton.get().getInstance(SuggestedUpdateTranscoder.class);
		this.sqsEmailQueue = GuiceSingleton.get().getInstanceWithName(String.class,
				"com.et.contacts.email.sqs.queue.name");
		this.contactsIndexName = GuiceSingleton.get().getInstanceWithName(String.class, "com.et.search.contacts_index.name");
	}

	@Override
	public void execute(Tuple input, BasicOutputCollector collector) {
		SuggestedUpdate update = (SuggestedUpdate) input.getValue(0);

		try {
            GetResponse idLookupResp = etTransportClient.executeGetRequest(
                    new GetRequest(contactsIndexName, "suggested_update", String.valueOf(update.getId()))
                            .routing(String.valueOf(update.getOid())))
                    .actionGet();
			long docVersion = idLookupResp.getVersion();

			if (!idLookupResp.isExists()) {
				indexToElasticSearch(update, docVersion);
			}

			enqueueEmailNotifications(update);
		} catch (Exception e) {
			throw new ReportedFailedException(e);
		}

		collector.emit(new Values(input.getValue(0)));
	}

	private void enqueueEmailNotifications(SuggestedUpdate update) {
		// Needed to support migrations of existing legacy data where we don't want to resend notifications
		if (update.getSkipEmailNotification()) {
			return;
		}

		String fromName = getFromName(update);
		String[] emails = getEmailsToSendTo(update);

		for (String email : emails) {
			SendMessageRequest req = buildRequest(buildEmailDescriptor(update, email, fromName));

			sqsClient.sendMessage(req);
		}
	}

	private SendMessageRequest buildRequest(JsonNode emailDescriptor) {
		return new SendMessageRequest(sqsEmailQueue, emailDescriptor.toString());
	}

	private String buildUniqueMsgId(SuggestedUpdate su, String toEmail) {
		return Hashing.md5().newHasher().putInt(su.getOid()).putLong(su.getTargetContactId())
				.putString(su.getSuggestion(), StandardCharsets.UTF_8).putInt(su.getUserId())
				.putString(EMAIL_NAME, StandardCharsets.UTF_8).putString(toEmail, StandardCharsets.UTF_8).hash()
				.toString();
	}

	private JsonNode buildEmailDescriptor(SuggestedUpdate su, String toEmail, String fromName) {
		JsonBuilder jb = new JsonBuilder();

		jb.startObject();

		jb.stringField("unique_msg_id", buildUniqueMsgId(su, toEmail));
		jb.numberField("oid", su.getOid());
		jb.stringField("from_email", "<EMAIL>");
		jb.stringField("from_name", fromName);
		jb.stringField("subject", "Suggested Update");
		jb.stringField("email_name", EMAIL_NAME);
		jb.stringField("to", toEmail);

		jb.startObjectWithName("context");
		jb.numberField("target_contact_id", su.getTargetContactId());
		jb.stringField("suggestion", su.getSuggestion());
		jb.numberField("reporting_user_id", su.getUserId());
		jb.endObject();

		jb.endObject();

		return jb.asJsonNode();
	}

	private String getFromName(SuggestedUpdate suggestedUpdate) {
		String fromName = dnaClient.getDNAForOrgByKey(suggestedUpdate.getOid(), AuthApiCreds.newAppCreds(secrets),
				"ET.Email.SenderName").asText();
		return fromName;
	}

	private String[] getEmailsToSendTo(SuggestedUpdate suggestedUpdate) {
		JsonNode respJson = dnaClient.getDNAForOrgByKey(suggestedUpdate.getOid(), AuthApiCreds.newAppCreds(secrets),
				"ET.Email.SuggestedUpdate.Recipients");
		String emailCsv = respJson.asText();

		return Iterables.toArray(Splitter.on(',').trimResults().omitEmptyStrings().split(emailCsv), String.class);
	}

	private void indexToElasticSearch(SuggestedUpdate update, long docVersion) {
		JsonBuilder jb = new JsonBuilder();
		jb.startObject();
		jb.numberField("id", update.getId());
		jb.numberField("oid", update.getOid());
		jb.numberField("user_id", update.getUserId());
		jb.numberField("created_at", update.getCreatedAt());
		jb.numberField("updated_at", update.getUpdatedAt());
		jb.numberField("target_contact_id", update.getTargetContactId());
		jb.stringField("suggestion", update.getSuggestion());

		jb.endObject();
		
        IndexRequest indexRequest = new IndexRequest(contactsIndexName, "suggested_update",
                String.valueOf(update.getId())).source(jb.asString()).routing(String.valueOf(update.getOid()));

		setOptimisticLockingControl(docVersion, indexRequest);

		etTransportClient.executeIndexRequest(indexRequest).actionGet();
	}

	private void setOptimisticLockingControl(long docVersion, IndexRequest indexRequest) {
		if (docVersion >= 0) {
			indexRequest.version(docVersion);
		} else {
		    indexRequest.create(true);
		}
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
	}
}
