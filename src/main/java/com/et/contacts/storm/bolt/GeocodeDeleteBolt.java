package com.et.contacts.storm.bolt;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.et.contacts.v1.data.ContactsProtoBuf;
import com.et.contacts.v1.data.ProtoWithListIndex;
import org.apache.log4j.Logger;

import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.storm.utils.CensusUtils;
import com.et.contacts.storm.utils.GeocodeUtils;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactChangeEvent;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObject;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObjectList;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValuePair;
import com.et.contacts.v1.data.ContactsProtoBuf.UpdateSource;
import com.et.contacts.v1.data.PropertyUtils;
import com.et.contacts.v1.data.property.NVPWithOptionalParent;
import com.et.contacts.v1.data.visitor.PropertyValueVisitor;
import com.et.contacts.v1.data.walker.ContactPropertyValueWalker;
import com.et.contacts.v1.data.wrapper.ContactDelta;
import com.et.contacts.v1.data.wrapper.ContactWrapper;
import com.google.common.base.Optional;
import com.google.common.collect.Sets;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;

public class GeocodeDeleteBolt extends BaseBasicBolt {
	private final static Logger LOG = Logger.getLogger(GeocodeDeleteBolt.class);

	private GeocodeUtils geocodeUtils;
	private CensusUtils  censusUtils;

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);
		geocodeUtils = GuiceSingleton.get().getInstance(GeocodeUtils.class);
		censusUtils  = GuiceSingleton.get().getInstance(CensusUtils.class);
	}

	@Override
	public void execute(Tuple input, BasicOutputCollector collector) {
		ContactChangeEvent event = (ContactChangeEvent) input.getValueByField("event");
		Contact currentContact = event.getCurrent();

		Optional<ProtoWithListIndex<NameValueObjectList>> maybeAddresses = PropertyUtils
				.getNvolistByName(currentContact, "addresses");

		if (!maybeAddresses.isPresent()) {
			return;
		}

		NameValueObjectList updatedAddresses = getAddressesWithDeletedGeocodeProperties(maybeAddresses.get().getProto());

		if (updatedAddresses.getNameValueObjectCount() == 0) {
			return;
		}

		Contact contactWithDeletedGeocode = currentContact
				.toBuilder()
				.clearIdentity()
				.clearRoleId()
				.clearNameValuePair()
				.clearNameValueObject()
				.clearNameValueObjectList()
				.addNameValueObjectList(updatedAddresses)
				.build();

		collector.emit(new Values(contactWithDeletedGeocode.getId(), contactWithDeletedGeocode));
	}

	private NameValueObjectList getAddressesWithDeletedGeocodeProperties(NameValueObjectList addresses) {
		NameValueObjectList.Builder addressesToUpdateBldr = addresses.toBuilder().clearNameValueObject();

		for (NameValueObject address : addresses.getNameValueObjectList()) {
			NameValueObject.Builder addrBldr = address.toBuilder().clearNameValuePair();
			boolean addressIsDeleted = true;
			for (ContactsProtoBuf.NameValuePair nvp : address.getNameValuePairList()) {
				if (!geocodeUtils.isGeocodeProperty(nvp.getProperty().getName())) {
					addressIsDeleted = false;
				}
			}

			for (ContactsProtoBuf.NameValuePair nvp : address.getNameValuePairList()) {
				if (geocodeUtils.isGeocodeProperty(nvp.getProperty().getName())) {
					if (addressIsDeleted) {
						addrBldr.addNameValuePair(nvp.toBuilder().setDeleted(true));
					}
				}
			}

			if (addrBldr.getNameValuePairCount() > 0) {
				addressesToUpdateBldr.addNameValueObject(addrBldr);
			}
		}

		return addressesToUpdateBldr.build();
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields("contact_id", "contact-to-update"));
	}
}
