package com.et.contacts.storm.bolt;

import java.util.Map;
import java.util.concurrent.ExecutionException;

import org.apache.log4j.Logger;

import com.et.contacts.client.ContactsClient;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.storm.utils.GeocodeUtils;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.UpdateSource;
import com.et.contacts.v1.data.NestedPropertyMap;
import com.et.contacts.v1.data.PermissionEvaluator;
import com.et.contacts.v1.data.PropertyUtils;
import com.et.contacts.v1.data.transcoder.ContactTranscoder;
import com.et.contacts.v1.data.transcoder.IdentityTranscoder;
import com.et.contacts.v1.data.transcoder.PropertyTranscoder;
import com.et.contacts.v1.data.transcoder.RoleTranscoder;
import com.et.exhale.http.HttpResponse;
import com.et.user.data.ApplicationKey;
import com.et.webcommon.auth.AuthApiCreds;
import com.et.webcommon.auth.AuthenticatedUser;
import com.google.common.base.Optional;
import com.google.common.cache.Cache;
import com.google.common.cache.LoadingCache;
import com.google.inject.TypeLiteral;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Tuple;
import static com.et.secrets.SecretsModule.SECRETS_MANAGER_SECRET;


public class BaseGeocodeUpdateBolt extends BaseBasicBolt {
	protected static final Logger LOG = Logger.getLogger(BaseGeocodeUpdateBolt.class);

	protected Map<String, String> secrets;
	protected ContactsClient contactsClient;
	protected AuthApiCreds authApiCreds;
	protected GeocodeUtils geocodeUtils;
	protected String appKey;
	protected Cache<String, ApplicationKey> appKeyCache;
	protected LoadingCache<Integer, NestedPropertyMap> propMapCache;

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);

		TypeLiteral<Map<String, String>> secretsTl = new TypeLiteral<Map<String, String>>() {};
		this.secrets = GuiceSingleton.get().getInstanceWithName(secretsTl, SECRETS_MANAGER_SECRET);

		contactsClient = GuiceSingleton.get().getInstance(ContactsClient.class);
		authApiCreds = AuthApiCreds.newAppCreds(secrets);
		geocodeUtils = GuiceSingleton.get().getInstance(GeocodeUtils.class);
		appKey = secrets.get("com.et.auth.app_key");

		TypeLiteral<Cache<String, ApplicationKey>> tl = new TypeLiteral<Cache<String, ApplicationKey>>() {};
		appKeyCache = GuiceSingleton.get().getInstanceWithName(tl, "ApplicationKeyCache");
		propMapCache = GuiceSingleton.get().getInstanceWithName(new TypeLiteral<LoadingCache<Integer, NestedPropertyMap>>() {},
                                                                "ContactsPropMapCache");
	}

	protected String getBoltMetricsHookName() {
		return "geocode_update_bolt";
	}

	@Override
	public void execute(Tuple input, BasicOutputCollector collector) {
		Contact contactToUpdate = (Contact) input.getValueByField("contact-to-update");

		updateContact(contactToUpdate, 1);
	}

	protected void updateContact(Contact contactToUpdate, int retry) {
		if (contactToUpdate.getNameValueObjectListCount() == 0) {
			return;
		}

        PropertyUtils.getNvolByName (contactToUpdate, "addresses");

        if (geocodeUtils.isAddressesEmpty (PropertyUtils.getNvolByName (contactToUpdate, "addresses"))) {
			return;
		}

		final int oid = contactToUpdate.getOid();
		final long contactId = contactToUpdate.getId();

		try {
			String contactJson = buildContactTranscoder().toJsonString(contactToUpdate, appKey,
					buildPermissionEvaluator());

			HttpResponse respFromUpdate = contactsClient.updateContactWithRawResponse(oid, authApiCreds, contactId,
					UpdateSource.HADOOP, contactJson);

			if (respFromUpdate.getStatusCode() == 409) {
			    if (retry > 2) {
                    throw new ReportedFailedException(
                            "Failed to update oid: " + contactToUpdate.getOid() + ", contact_id: "
                                    + contactToUpdate.getId() + ", with updated_at: " + contactToUpdate.getUpdatedAt()
                                    + ", response body: " + respFromUpdate.getResponseAsString());
			    }

				handleContactUpdateConflict(contactToUpdate, retry);
			} else if (respFromUpdate.isClientError()) {
				LOG.error(respFromUpdate.getResponseAsString());
			} else if (respFromUpdate.isServerError()) {
				throw new ReportedFailedException("Failed to update oid: " + oid + ", contact: " + contactJson
						+ ", status code: " + respFromUpdate.getStatusCode());
			}

		} catch (ReportedFailedException e) {
		    LOG.error(String.format("failed to geocode address from oid: %d contactId: %d", contactToUpdate.getOid(), contactToUpdate.getId()), e);
		    throw e;

		} catch (Exception e) {
			throw new ReportedFailedException(e);
		}
	}

	private void handleContactUpdateConflict(Contact contactToUpdate, int retry) throws ExecutionException {
		Contact contactFromLookup = contactsClient.getContactById(contactToUpdate.getOid(), authApiCreds,
				contactToUpdate.getId(), Optional.<UpdateSource> absent(), propMapCache.get(contactToUpdate.getOid()));
		contactToUpdate = getContactWithAddressesUpdatedAndTimestampMerged(contactToUpdate, contactFromLookup);

		if (PropertyUtils.getNvolByName (contactToUpdate, "addresses").getNameValueObjectCount () == 0) {
			return;
		}

		updateContact(contactToUpdate, retry + 1);
	}

	protected Contact getContactWithAddressesUpdatedAndTimestampMerged(Contact contactToUpdate, Contact contactFromLookup) {
	    Contact.Builder mergedBldr = contactToUpdate.toBuilder();
	    mergedBldr.setUpdatedAt(contactFromLookup.getUpdatedAt());

	    // Prevent accidentally suppressing contact level privacy
	    if (contactToUpdate.getPrivate() != contactFromLookup.getPrivate()) {
	        mergedBldr.setPrivate(contactFromLookup.getPrivate());
	    }

		return mergedBldr.build();
	}

	private ContactTranscoder buildContactTranscoder() {
		return new ContactTranscoder(buildPropertyTranscoder(), new RoleTranscoder(), new IdentityTranscoder());
	}

	private PropertyTranscoder buildPropertyTranscoder() {
		return GuiceSingleton.get().getInstance(PropertyTranscoder.class);
	}

	private PermissionEvaluator buildPermissionEvaluator() {
		return new PermissionEvaluator(buildSuperUser(), appKeyCache);
	}

	private AuthenticatedUser buildSuperUser() {
		return new AuthenticatedUser(0, null, null, null, null, true, null, null, Optional.<Integer>absent());
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
	}
}
