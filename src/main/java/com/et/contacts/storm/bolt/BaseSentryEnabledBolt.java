package com.et.contacts.storm.bolt;

import java.util.Map;

import net.kencochrane.raven.Raven;
import net.kencochrane.raven.event.Event;
import net.kencochrane.raven.event.EventBuilder;
import net.kencochrane.raven.event.interfaces.ExceptionInterface;
import backtype.storm.task.TopologyContext;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Tuple;

import com.et.contacts.storm.guice.GuiceSingleton;

public abstract class BaseSentryEnabledBolt extends BaseBasicBolt {
	private Raven sentryClient;

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);

		sentryClient = GuiceSingleton.get().getInstance(Raven.class);
	}

	protected void reportException(Exception error, int oid, Map<String, Object> extraFields) {
		if (sentryClient != null) {
			try {
				EventBuilder eventBuilder = new EventBuilder().setMessage(error.getMessage())
						.setLevel(Event.Level.ERROR).addTag("oid", String.valueOf(oid))
						.addTag("component", "contacts_storm").setLogger(getClass().getCanonicalName())
						.addSentryInterface(new ExceptionInterface(error));

				for (String key : extraFields.keySet()) {
					eventBuilder.addExtra(key, extraFields.get(key));
				}

				sentryClient.runBuilderHelpers(eventBuilder);
				sentryClient.sendEvent(eventBuilder.build());
			} catch (Exception ex) {
				throw new ReportedFailedException("Could not report error to sentry", ex);
			}
		}
	}

	protected String getTupleValueAsStringNoErrorCheck(Tuple input, int index) {
		try {
			return (String) input.getString(index);
		} catch (Exception e) {
			return "Could not get String value from tuple";
		}
	}
}