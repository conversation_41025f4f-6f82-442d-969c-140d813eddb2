package com.et.contacts.storm.bolt;

import java.util.Map;
import java.util.Set;

import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObject;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObjectList;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValuePair;
import com.et.contacts.v1.data.PropertyUtils;
import com.et.contacts.v1.data.visitor.PropertyValueVisitor;
import com.et.contacts.v1.data.walker.ContactPropertyValueWalker;
import com.google.common.base.Optional;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;

import backtype.storm.task.TopologyContext;

public class GeocodeUpdateHandlingPrivAndDelBolt extends BaseGeocodeUpdateBolt {

    @Override
    public void prepare(Map stormConf, TopologyContext context) {
        super.prepare(stormConf, context);
    }

    @Override
    protected String getBoltMetricsHookName() {
        return "geocode_update_change_bolt";
    }

    @Override
    protected Contact getContactWithAddressesUpdatedAndTimestampMerged(Contact contactToUpdate, Contact contactFromLookup) {
        Contact contactWithAddressesUpdatedAndTimestampMerged = super.getContactWithAddressesUpdatedAndTimestampMerged(
                contactToUpdate, contactFromLookup);
        contactWithAddressesUpdatedAndTimestampMerged = getContactWithoutDeleted(
                contactWithAddressesUpdatedAndTimestampMerged, contactFromLookup);
        contactWithAddressesUpdatedAndTimestampMerged = getContactWithPrivacyMerged(
                contactWithAddressesUpdatedAndTimestampMerged, contactFromLookup);
        contactWithAddressesUpdatedAndTimestampMerged = getContactOnlyWithChanges(
                contactWithAddressesUpdatedAndTimestampMerged, contactFromLookup);
        return contactWithAddressesUpdatedAndTimestampMerged;
    }

    private Contact getContactWithoutDeleted(Contact contactToUpdate, Contact contactFromLookup) {
        final Set<String> presentAddressGuidSet = Sets.newHashSet();
        new ContactPropertyValueWalker().walk(contactFromLookup, new PropertyValueVisitor() {
            @Override
            public void visit(NameValuePair nvp, Optional<NameValueObject> parentObject) {
                if (parentObject.isPresent() && parentObject.get().getProperty().getName().equals("address")) {
                    presentAddressGuidSet.add(parentObject.get().getGuid());
                }
            }
        });

        NameValueObjectList addresses = PropertyUtils.getNvolByName (contactToUpdate, "addresses");
        NameValueObjectList.Builder addressesBldr = addresses.toBuilder().clearNameValueObject();

        for (NameValueObject address : addresses.getNameValueObjectList()) {
            if (presentAddressGuidSet.contains(address.getGuid())) {
                addressesBldr.addNameValueObject(address);
            }
        }

        return contactToUpdate.toBuilder().setNameValueObjectList(0, addressesBldr).build();
    }

    private Contact getContactWithPrivacyMerged(Contact contactToUpdate, Contact contactFromLookup) {
        final Set<String> privateAddressGuidSet = Sets.newHashSet();
        new ContactPropertyValueWalker().walk(contactFromLookup, new PropertyValueVisitor() {
            @Override
            public void visit(NameValuePair nvp, Optional<NameValueObject> parentObject) {
                if (nvp.getPrivate() && parentObject.isPresent()
                        && parentObject.get().getProperty().getName().equals("address")) {
                    privateAddressGuidSet.add(parentObject.get().getGuid());
                }
            }
        });

        NameValueObjectList.Builder addressesBldr = PropertyUtils.getNvolByName (contactToUpdate, "addresses").toBuilder ();

        for (NameValueObject.Builder addressBldr : addressesBldr.getNameValueObjectBuilderList()) {
            String addressGuid = addressBldr.getGuid();
            boolean addressIsPrivate = privateAddressGuidSet.contains(addressGuid);

            for (NameValuePair.Builder nvpBldr : addressBldr.getNameValuePairBuilderList()) {
                nvpBldr.setPrivate(addressIsPrivate);
            }
        }

        return contactToUpdate.toBuilder().setNameValueObjectList(0, addressesBldr).build();
    }

    private Contact getContactOnlyWithChanges(Contact contactToUpdate, Contact contactFromLookup) {
        final Table<String, String, NameValuePair> propGuidTab = HashBasedTable.create();

        new ContactPropertyValueWalker().walk(contactFromLookup, new PropertyValueVisitor() {
            @Override
            public void visit(NameValuePair nvp, Optional<NameValueObject> parentObject) {
                if (parentObject.isPresent() && parentObject.get().getProperty().getName().equals("address")) {
                	propGuidTab.put(nvp.getProperty().getName(), parentObject.get().getGuid(), nvp);
                }
            }
        });

        NameValueObjectList.Builder addressesBldr = PropertyUtils.getNvolByName (contactToUpdate, "addresses").toBuilder ();

        for (NameValueObject address : addressesBldr.getNameValueObjectList()) {
            NameValueObject.Builder addressBldr = address.toBuilder().clearNameValuePair();
            for (NameValuePair nvp : address.getNameValuePairList()) {

				if (!propGuidTab.contains(nvp.getProperty().getName(), address.getGuid())
						|| geocodeHasChanged(propGuidTab.get(nvp.getProperty().getName(), address.getGuid()), nvp)) {
            		addressBldr.addNameValuePair(nvp);
            	}
            }
        }

        return contactToUpdate.toBuilder().setNameValueObjectList(0, addressesBldr).build();
    }

    private boolean geocodeHasChanged(NameValuePair previous, NameValuePair current) {
        return !(previous.getValue().equals(current.getValue()) && previous.getPrivate() == current.getPrivate());
    }
}
