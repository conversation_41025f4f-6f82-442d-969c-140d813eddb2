package com.et.contacts.storm.bolt;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;
import com.et.contacts.es.indexers.ElasticSearchContactIndexer;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactChangeEvent;
import com.et.contacts.v1.data.NestedPropertyMap;
import com.google.common.cache.LoadingCache;
import com.google.inject.TypeLiteral;
import org.apache.log4j.Logger;
import org.elasticsearch.index.IndexNotFoundException;

import java.util.Map;

@SuppressWarnings("serial")
public class ElasticSearchIndexBolt extends BaseBasicBolt {
	private final static Logger LOG = Logger.getLogger(ElasticSearchIndexBolt.class);

	private ElasticSearchContactIndexer esContactIndexer;
	private LoadingCache<Integer, NestedPropertyMap> orgPropCache;

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);
		esContactIndexer = GuiceSingleton.get().getInstance(ElasticSearchContactIndexer.class);
		orgPropCache = GuiceSingleton.get().getInstanceWithName(
                new TypeLiteral<LoadingCache<Integer, NestedPropertyMap>>() {
                }, "ContactsPropMapCache");
	}

	@Override
	public void execute(Tuple input, BasicOutputCollector collector) {
		ContactChangeEvent event = (ContactChangeEvent) input.getValueByField("event");
		Contact contact = event.getCurrent();
		try {
			NestedPropertyMap nestedPropMap = orgPropCache.get(contact.getOid());

			if (shouldContactBeDeletedFromElasticSearch(event, contact)) {
				esContactIndexer.deleteFromElasticSearch(contact);

			} else if (event.getType() == ContactChangeEvent.Type.REFRESH) {
				esContactIndexer.indexToElasticSearch(contact, nestedPropMap);

			} else {
				esContactIndexer.indexToElasticSearchIfNecessary(contact, nestedPropMap);
			}

			collector.emit(new Values(input.getLongByField("contact_id"), event));

		} catch (IndexNotFoundException e) {
			// the oid alias may have been deleted
			collector.emit(new Values(input.getLongByField("contact_id"), event));

		} catch (Exception e) {
			LOG.error("Failed processing contact id: " + contact.getId() + ", oid: " + contact.getOid());
			LOG.error(e, e);

			throw new ReportedFailedException(e);
		}
	}

    private boolean shouldContactBeDeletedFromElasticSearch(ContactChangeEvent event, Contact currentContact) {
        return event.getType() == ContactChangeEvent.Type.DELETE || currentContact.getDeleted();
    }

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields("contact_id", "event"));
	}
}
