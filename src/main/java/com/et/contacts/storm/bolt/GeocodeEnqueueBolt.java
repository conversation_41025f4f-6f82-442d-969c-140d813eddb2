package com.et.contacts.storm.bolt;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseBasicBolt;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;
import com.datastax.driver.core.ConsistencyLevel;
import com.datastax.driver.core.PreparedStatement;
import com.datastax.driver.core.Row;
import com.datastax.driver.core.Session;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.storm.utils.CensusUtils;
import com.et.contacts.storm.utils.GeocodeUtils;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactChangeEvent;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObject;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObjectList;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValuePair;
import com.et.contacts.v1.data.PropertyUtils;
import com.et.contacts.v1.data.ProtoWithListIndex;
import com.et.contacts.v1.data.property.NVPWithOptionalParent;
import com.et.contacts.v1.data.wrapper.ContactDelta;
import com.et.contacts.v1.data.wrapper.ContactWrapper;
import com.et.json.JsonBuilder;
import com.et.json.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Optional;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.inject.TypeLiteral;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.PartitionInfo;
import org.apache.log4j.Logger;

import java.nio.ByteBuffer;
import java.util.List;
import java.util.Map;

public class GeocodeEnqueueBolt extends BaseBasicBolt {
	private final static Logger LOG = Logger.getLogger(GeocodeEnqueueBolt.class);

	private KafkaProducer<byte[], byte[]> kafkaProducer;
	private String kafkaTopic;
	private GeocodeUtils geocodeUtils;
	private Session geocodeKsSession;
	private PreparedStatement selectFromCacheStmt;
	private CensusUtils censusUtils;

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);
		kafkaProducer = GuiceSingleton.get()
				.getInstanceWithName(new TypeLiteral<KafkaProducer<byte[], byte[]>>() {
				}, "KafkaProducer");
		kafkaTopic = GuiceSingleton.get().getInstanceWithName(String.class, "com.et.contacts.kafka.topic.contacts_geocode");
		geocodeUtils = GuiceSingleton.get().getInstance(GeocodeUtils.class);
		geocodeKsSession = GuiceSingleton.get().getInstanceWithName(Session.class, "GeocodesKsSession");
		selectFromCacheStmt = geocodeKsSession
				.prepare("SELECT addr_md5, addr_str, payload, WRITETIME(payload) AS writetime FROM cache WHERE addr_md5 = ?")
				.setConsistencyLevel(
						ConsistencyLevel.ONE);
		censusUtils = GuiceSingleton.get().getInstance(CensusUtils.class);
	}

	@Override
	public void execute(Tuple input, BasicOutputCollector collector) {
		ContactChangeEvent event = (ContactChangeEvent) input.getValueByField("event");

		Contact currentContact = event.getCurrent();
		Contact previousContact = event.getPrevious();

		NameValueObjectList addresses;

		if (event.getType() == ContactChangeEvent.Type.DELETE) {
			//don't enqueue a contact that has been deleted
			return;
		}

		Optional<ProtoWithListIndex<NameValueObjectList>> maybeAddresses = PropertyUtils
				.getNvolistByName(currentContact, "addresses");

		if (!maybeAddresses.isPresent()) {
			return;
		}

		NameValueObjectList addressesToGeocode;
		if (event.getType() == ContactChangeEvent.Type.REFRESH) {
			addressesToGeocode = maybeAddresses.get().getProto();

		} else {
			ContactDelta delta;
			try {
				delta = new ContactDelta(new ContactWrapper(currentContact), new ContactWrapper(previousContact));
			} catch (Exception e) {
				LOG.error(e, e);
				return;
			}

			List<NVPWithOptionalParent> newValuesAndChanges = delta.getNewNvpValues();
			newValuesAndChanges.addAll(delta.getNvpValueChanges());
			List<NVPWithOptionalParent> deletedValues = delta.getNvpDeletionChanges();

			addressesToGeocode = getAddressesToGeocode(maybeAddresses.get().getProto(),
					newValuesAndChanges, deletedValues);
		}

		if (addressesToGeocode.getNameValueObjectCount() == 0) {
			return;
		}

		NameValueObjectList.Builder addressesToEnqueueBldr = addressesToGeocode.toBuilder().clearNameValueObject();
		NameValueObjectList.Builder addressesFromCacheBldr = addressesToGeocode.toBuilder().clearNameValueObject();

		dispatchAddressesToEnqueue(addressesToGeocode, addressesToEnqueueBldr, addressesFromCacheBldr);

		if (addressesToEnqueueBldr.getNameValueObjectCount() > 0) {
			NameValueObjectList previousAddresses = null;
			if (previousContact != null) {
				for (NameValueObjectList nvol : previousContact.getNameValueObjectListList()) {
					if (nvol.getProperty().getName().equals("addresses")) {
						previousAddresses = nvol;
						break;
					}
				}
			}

			NameValueObjectList currentAddresses = null;
			if (currentContact != null) {
				for (NameValueObjectList nvol : currentContact.getNameValueObjectListList()) {
					if (nvol.getProperty().getName().equals("addresses")) {
						currentAddresses = nvol;
						break;
					}
				}
			}

			Contact contactToEnqueue = currentContact
					.toBuilder()
					.clearIdentity()
					.clearRoleId()
					.clearNameValuePair()
					.clearNameValueObject()
					.clearNameValueObjectList()
					.addNameValueObjectList(addressesToEnqueueBldr)
					.build();

			enqueue(contactToEnqueue);
		}

		if (addressesFromCacheBldr.getNameValueObjectCount() > 0) {
			Contact contactToUpdate = currentContact
					.toBuilder()
					.clearIdentity()
					.clearRoleId()
					.clearNameValuePair()
					.clearNameValueObject()
					.clearNameValueObjectList()
					.addNameValueObjectList(addressesFromCacheBldr)
					.build();

			collector.emit(new Values(input.getLongByField("contact_id"), contactToUpdate));
		}
	}

	private void dispatchAddressesToEnqueue(NameValueObjectList addressesToGeocode,
			NameValueObjectList.Builder addressesToEnqueueBldr,
			NameValueObjectList.Builder addressesFromCacheBldr) {
		for (NameValueObject.Builder addressBldr : addressesToGeocode.toBuilder().getNameValueObjectBuilderList()) {
			String addressString = geocodeUtils.getAddressString(addressBldr.build(), false);
			if (Strings.isNullOrEmpty(addressString)) {
				continue;
			}

			List<Row> existingGeocodes = geocodeUtils
					.checkGeocodeCache(geocodeKsSession, selectFromCacheStmt, addressString);
			if (existingGeocodes.isEmpty()) {
				addressesToEnqueueBldr.addNameValueObject(addressBldr);
				continue;
			}

			Row existingGeocode = existingGeocodes.get(0);
			JsonNode cachedPayload = JsonUtils.fromString(existingGeocode.getString("payload"));
			Map<String, Double> geocodedAddress = geocodeUtils.getGeocodeFromPayload(cachedPayload);

			// If no geocode could be resolved, skip
			if (geocodedAddress.isEmpty()) {
				continue;
			}

			geocodeUtils.addGeocodedAndCensusDataToAddress(addressBldr, geocodedAddress, censusUtils);
			addressesFromCacheBldr.addNameValueObject(addressBldr);
		}
	}

	private NameValueObjectList getAddressesToGeocode(NameValueObjectList addresses,
			List<NVPWithOptionalParent> newValuesAndChanges, List<NVPWithOptionalParent> deletedValues) {
		NameValueObjectList.Builder addressesToGeocodeBldr = addresses.toBuilder().clearNameValueObject();

		Map<String, NameValueObject> addressesToGeocode = Maps.newHashMap();
		Map<String, NameValueObject> currentAddresses = Maps.newHashMap();
		for (NameValueObject address : addresses.getNameValueObjectList()) {
			currentAddresses.put(address.getGuid(), address);
		}

		// handle new or changed values
		for (NVPWithOptionalParent newValueOrChange : newValuesAndChanges) {
			Optional<NameValueObject> maybeNvo = newValueOrChange.getNvo();
			if (!maybeNvo.isPresent() || !maybeNvo.get().getProperty().getName().equals("address")) {
				continue;
			}

			if (geocodeUtils.isGeocodeProperty(newValueOrChange.getNvp().getProperty().getName())) {
				continue;
			}

			addressesToGeocode.put(maybeNvo.get().getGuid(), maybeNvo.get());
		}

		// handle deleted values
		for (NVPWithOptionalParent deletedValue : deletedValues) {
			Optional<NameValueObject> maybeNvo = deletedValue.getNvo();
			if (!maybeNvo.isPresent() || !maybeNvo.get().getProperty().getName().equals("address")) {
				continue;
			}

			if (geocodeUtils.isGeocodeProperty(deletedValue.getNvp().getProperty().getName())) {
				continue;
			}

			if (!currentAddresses.containsKey(maybeNvo.get().getGuid())) {
				continue;
			}

			addressesToGeocode.put(maybeNvo.get().getGuid(), currentAddresses.get(maybeNvo.get().getGuid()));
		}

		for (NameValueObject address : addressesToGeocode.values()) {
			addressesToGeocodeBldr.addNameValueObject(address);
		}

		return addressesToGeocodeBldr.build();
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields("contact_id", "contact-to-update"));
	}

	private void enqueue(Contact contactToEnqueue) {
		try {
			Long contactID = contactToEnqueue.getId();
			List<PartitionInfo> partitions = kafkaProducer.partitionsFor(kafkaTopic);
			PartitionInfo partitionInfo = partitions.get((int) (contactID % partitions.size()));

			ProducerRecord<byte[], byte[]> producerRecord = new ProducerRecord(
					kafkaTopic,
					partitionInfo.partition(),
					ByteBuffer.allocate(Long.SIZE).putLong(contactID).array(),
					contactToEnqueue.toByteArray());

			kafkaProducer.send(producerRecord, new Callback() {
				private ProducerRecord<byte[], byte[]> producerRecord;
				@Override
				public void onCompletion(RecordMetadata metadata, Exception exception) {
					if (exception != null) {
						throw new ReportedFailedException(exception);
					}
				}
				private Callback forProducerRecord(ProducerRecord<byte[], byte[]> producerRecord) {
					this.producerRecord = producerRecord;
					return this;
				}
			}.forProducerRecord(producerRecord)).get();
		} catch (Exception e) {
			throw new ReportedFailedException(e);
		}
	}
}
