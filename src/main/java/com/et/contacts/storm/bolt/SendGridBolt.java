package com.et.contacts.storm.bolt;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

import javax.sql.DataSource;

import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.ResultSetHandler;
import org.apache.log4j.Logger;

import com.et.contacts.storm.email.EmailRenderer;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.json.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.sendgrid.SendGrid;
import com.google.common.base.Charsets;
import com.google.common.collect.Maps;
import com.google.common.hash.Hashing;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;

@SuppressWarnings("unchecked")
public class SendGridBolt extends BaseSentryEnabledBolt {
	private static final Logger log = Logger.getLogger(SendGridBolt.class);
	private DataSource ds;
	private String sgUserName;
	private String sgPass;

	@Override
	public void prepare(Map stormConf, TopologyContext context) {
		super.prepare(stormConf, context);

		ds = GuiceSingleton.get().getInstance(DataSource.class);
		sgUserName = GuiceSingleton.get().getInstanceWithName(String.class, "com.et.sendgrid.user");
		sgPass = GuiceSingleton.get().getInstanceWithName(String.class, "com.et.sendgrid.pass");
	}

	@Override
	public void execute(Tuple input, BasicOutputCollector collector) {
		String providedMsgId = (String) input.getValue(0);
		int oid = 0;

		try {
			JsonNode msgDescriptor = JsonUtils.fromString((String) input.getValue(1));

			String to = msgDescriptor.get("to").asText();
			oid = msgDescriptor.get("oid").asInt();
			String emailName = msgDescriptor.get("email_name").asText();

			boolean locked = attemptLock(providedMsgId, oid, to, emailName);
			if (locked) {
				routeEmailDependingOnEnv(providedMsgId, msgDescriptor);
			} else {
				log.info("Message has already been sent: " + emailName + " with id: " + providedMsgId + ", to: " + to);
			}
		} catch (Exception e) {
			Map<String, Object> extras = Maps.newHashMap();
			extras.put("provided_msg_id", getTupleValueAsStringNoErrorCheck(input, 0));
			extras.put("msg_descriptor", getTupleValueAsStringNoErrorCheck(input, 1));

			reportException(e, oid, extras);
			throw new ReportedFailedException(e);
		}

		collector.emit(new Values(input.getValue(1)));
	}

	private void routeEmailDependingOnEnv(String providedMsgId, JsonNode msgDescriptor) throws SQLException {
		String emailName = msgDescriptor.get("email_name").asText();
		int oid = msgDescriptor.get("oid").asInt();
		String to = msgDescriptor.get("to").asText();

		EmailRenderer<JsonNode> renderer = getEmailRenderer(emailName);
		String textBody = renderer.renderTextBody(msgDescriptor);
		boolean sentSuccess = false;

		if ("prod".equals(System.getProperty("com.et.env"))) {
			sentSuccess = routeToSendGrid(providedMsgId, msgDescriptor, textBody);
		} else if ("stage".equals(System.getProperty("com.et.env"))) {
			sentSuccess = routeToLogFile(providedMsgId, msgDescriptor, textBody);
		} else {
			sentSuccess = routeToLogFile(providedMsgId, msgDescriptor, textBody);
		}

		if (sentSuccess) {
			recordSentState(providedMsgId, oid, to, emailName);
		} else {
			removeSendAttemptState(providedMsgId, oid, to, emailName);
		}
	}

	private boolean routeToLogFile(String providedMsgId, JsonNode msgDescriptor, String textBody) {
		log.info("------------ BEGIN Routing email to log file -------------");
		log.info("Provided message id: " + providedMsgId);
		log.info("Raw message descriptor: " + msgDescriptor.toString());
		log.info("Message Body: ");
		System.out.println(textBody);
		log.info("------------ END Routing email to log file -------------");

		return true;
	}

	private boolean routeToSendGrid(String providedMsgId, JsonNode msgDescriptor, String textBody) {
		String emailName = msgDescriptor.get("email_name").asText();
		String to = msgDescriptor.get("to").asText();
		String subject = msgDescriptor.get("subject").asText();
		String fromEmail = msgDescriptor.get("from_email").asText();
		String fromName = msgDescriptor.get("from_name").asText();

		SendGrid sg = new SendGrid(sgUserName, sgPass);
		sg.addTo(to);
		sg.setFrom(fromEmail);
		sg.setFromName(fromName);
		sg.setSubject(subject);
		sg.setText(textBody);

		log.info("Sending email: " + emailName + " with id: " + providedMsgId + ", to: " + to);
		String jsonResp = sg.send();
		JsonNode resp = JsonUtils.fromString(jsonResp);

		if ("success".equals(resp.get("message").asText())) {
			return true;
		} else {
			return false;
		}
	}

	private EmailRenderer<JsonNode> getEmailRenderer(String emailName) {
		return GuiceSingleton.get().getInstanceWithName(EmailRenderer.class, emailName);
	}

	private void removeSendAttemptState(String providedMsgId, int oid, String to, String emailName) throws SQLException {
		new QueryRunner(ds).update(
				"DELETE FROM mail_event WHERE hashed_msg_id = ? AND oid = ? AND to_email = ? AND email = ?",
				md5(providedMsgId), oid, to, emailName);
	}

	private void recordSentState(String providedMsgId, int oid, String email, String emailName) throws SQLException {
		new QueryRunner(ds).update(
				"INSERT INTO mail_event (hashed_msg_id, provided_msg_id, oid, to_email, state, created_at, email_name) "
						+ "VALUES (?,?,?,?,?,?,?)", md5(providedMsgId), providedMsgId, oid, email, "SENT",
				System.currentTimeMillis(), emailName);
	}

	private String md5(String providedMsgId) {
		return Hashing.md5().newHasher().putString(providedMsgId, Charsets.UTF_8).hash().toString();
	}

	private boolean attemptLock(String msgId, int oid, String email, String emailName) throws SQLException {
		boolean sendLockExists = new QueryRunner(ds).query(
				"SELECT * FROM mail_event WHERE hashed_msg_id = ? AND oid = ?", new ResultSetHandler<Boolean>() {

					@Override
					public Boolean handle(ResultSet rs) throws SQLException {
						return rs.next();
					}
				}, md5(msgId), oid);

		if (!sendLockExists) {
			int recordsInserted = new QueryRunner(ds).update(
					"INSERT INTO mail_event (hashed_msg_id, provided_msg_id, oid, to_email, state, created_at, email_name) "
							+ "VALUES (?,?,?,?,?,?,?)", md5(msgId), msgId, oid, email, "SENDING",
					System.currentTimeMillis(), emailName);

			if (recordsInserted > 0) {
				return true;
			}
		}

		return false;
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields("msg_descriptor"));
	}
}
