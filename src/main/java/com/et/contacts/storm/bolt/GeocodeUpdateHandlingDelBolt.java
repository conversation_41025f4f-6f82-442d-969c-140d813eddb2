package com.et.contacts.storm.bolt;

import java.util.Set;

import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObject;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObjectList;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValuePair;
import com.et.contacts.v1.data.visitor.PropertyValueVisitor;
import com.et.contacts.v1.data.walker.ContactPropertyValueWalker;
import com.google.common.base.Optional;
import com.google.common.collect.Sets;

public class GeocodeUpdateHandlingDelBolt extends BaseGeocodeUpdateBolt {

	@Override
	protected String getBoltMetricsHookName() {
		return "geocode_update_delete_bolt";
	}

	@Override
	protected Contact getContactWithAddressesUpdatedAndTimestampMerged(Contact contactToUpdate, Contact contactFromLookup) {
		Contact contactWithAddressesUpdatedAndTimestampMerged = super.getContactWithAddressesUpdatedAndTimestampMerged(
				contactToUpdate, contactFromLookup);
		contactWithAddressesUpdatedAndTimestampMerged = getContactWithoutDeleted(
				contactWithAddressesUpdatedAndTimestampMerged, contactFromLookup);
		return contactWithAddressesUpdatedAndTimestampMerged;
	}

	private Contact getContactWithoutDeleted(Contact contactToUpdate, Contact contactFromLookup) {
		final Set<String> presentAddressGuidSet = Sets.newHashSet();
		new ContactPropertyValueWalker().walk(contactFromLookup, new PropertyValueVisitor() {
			@Override
			public void visit(NameValuePair nvp, Optional<NameValueObject> parentObject) {
				if (geocodeUtils.isGeocodeProperty(nvp.getProperty().getName())) {
					presentAddressGuidSet.add(parentObject.get().getGuid());
				}
			}
		});

		NameValueObjectList addresses = contactToUpdate.getNameValueObjectList(0);
		NameValueObjectList.Builder addressesBldr = addresses.toBuilder().clearNameValueObject();

		for (NameValueObject address : addresses.getNameValueObjectList()) {
			if (presentAddressGuidSet.contains(address.getGuid())) {
				addressesBldr.addNameValueObject(address);
			}
		}

		return contactToUpdate.toBuilder().setNameValueObjectList(0, addressesBldr).build();
	}
}
