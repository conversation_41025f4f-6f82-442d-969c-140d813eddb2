package com.et.contacts.storm.bolt;

import backtype.storm.task.TopologyContext;
import backtype.storm.topology.BasicOutputCollector;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Tuple;
import backtype.storm.tuple.Values;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.v1.data.ContactsProtoBuf;
import com.et.exhale.http.HttpClient;
import com.et.exhale.http.HttpRequest;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.inject.TypeLiteral;
import org.apache.log4j.Logger;
import static com.et.secrets.SecretsModule.SECRETS_MANAGER_SECRET;

import java.util.List;
import java.util.Map;

public class UpdateEventEngagementsForEmailBolt extends BaseSentryEnabledBolt {
    private static final Logger LOG = Logger.getLogger(UpdateEventEngagementsForEmailBolt.class);

    private HttpClient httpClient;
    private String eventsUrl;
    private Map<String, String> secrets;

    public UpdateEventEngagementsForEmailBolt(String eventsUrl) {
        this.eventsUrl = eventsUrl;
    }

    public void declareOutputFields(OutputFieldsDeclarer declarer) {
        declarer.declare(new Fields("contact_id", "event"));
    }

    @Override
    public void prepare(Map stormConf, TopologyContext context) {
        super.prepare(stormConf, context);
        httpClient = HttpClient.newBuilder().setConnectionTimeout(10000).setRequestTimeout(30000).build();
        TypeLiteral<Map<String, String>> tl = new TypeLiteral<Map<String, String>>() {};
        secrets = GuiceSingleton.get().getInstanceWithName(tl, SECRETS_MANAGER_SECRET);
    }

    public void execute(Tuple tuple, BasicOutputCollector collector) {
        ContactsProtoBuf.ContactChangeEvent event = (ContactsProtoBuf.ContactChangeEvent) tuple.getValueByField("event");
        ContactsProtoBuf.Contact currContact = event.getCurrent();
        ContactsProtoBuf.Contact prevContact = event.getPrevious();

        if (currContact != null) {
            int oid = currContact.getOid();
            Long contactId = currContact.getId();
            String op = "match";
            List<String> newEmails;

            try {
                List<String> currContactEmails = getContactEmails(currContact);
                List<String> prevContactEmails = getContactEmails(prevContact);

                newEmails = getNewlyAddedEmails(currContactEmails, prevContactEmails);
            } catch (Exception e) {
                String message = "An error occurred when getting newly added emails for a contact. oid=" + oid + ", contact_id=" + contactId+ ":";
                logAndReportException(message, e, oid, contactId);
                throw new ReportedFailedException(e);
            }

            for (String email : newEmails) {
                try {
                    String url = eventsUrl + "/v1/match/eventbrite";
                    HttpRequest eventsRequest = HttpRequest.newBuilder(HttpRequest.RequestType.POST, url)
                            .addQueryParam("oid", oid)
                            .addQueryParam("contact_id", contactId)
                            .addQueryParam("email", email)
                            .addQueryParam("op", op)
                            .addOrSetHeader("Application-Key", secrets.get("com.et.auth.app_key"))
                            .addOrSetHeader("Authorization", secrets.get("com.et.auth.super_user_token"))
                            .addOrSetHeader("Authorization-Provider", "EvertrueAppToken")
                            .addOrSetHeader("Content-Type", "application/json")
                            .build();
                    httpClient.execute(eventsRequest);
                } catch (Exception e) {
                    String message = "An error occurred when getting newly added emails for a contact. oid=" + oid + ", contact_id=" + contactId+ ":";
                    logAndReportException(message, e, oid, contactId);
                }
            }
        }

        collector.emit(new Values(tuple.getLongByField("contact_id"), event));
    }

    private void logAndReportException(String message, Exception e, int oid, Long contactId) {
        LOG.error(message , e);

        Map<String, Object> extras = Maps.newHashMap();
        extras.put("contact_id", contactId);

        reportException(e, oid, extras);
    }

    private List<String> getContactEmails(ContactsProtoBuf.Contact contact) {
        for (ContactsProtoBuf.NameValueObjectList nvol : contact.getNameValueObjectListList()) {
            if (nvol.getProperty().getName().equals("emails")) {
                List<String> emails = Lists.newArrayList();

                for (ContactsProtoBuf.NameValueObject nvo : nvol.getNameValueObjectList()) {
                    for (ContactsProtoBuf.NameValuePair nvp : nvo.getNameValuePairList()) {
                        if (nvp.getProperty().getName().equals("email")) {
                            emails.add(nvp.getValue());
                            break;
                        }
                    }
                }

                return emails;
            }
        }

        return Lists.newArrayList();
    }

    private List<String> getNewlyAddedEmails(List<String> currContactEmails, List<String> prevContactEmails) {
        List<String> newEmails = Lists.newArrayList();

        for (String newEmail : currContactEmails) {
            Boolean emailAlreadyExists = false;

            for (String oldEmail : prevContactEmails) {
                emailAlreadyExists = emailAlreadyExists || (newEmail.equals(oldEmail));
            }

            if (!emailAlreadyExists) {
                newEmails.add(newEmail);
            }
        }

        return newEmails;
    }
}
