package com.et.contacts.storm.guice;

import com.codahale.metrics.MetricRegistry;
import com.et.contacts.storm.dw.ContactsStormConfiguration;
import com.evertrue.dropwizard.hibernateoutside.HibernateCredentials;
import com.evertrue.dropwizard.hibernateoutside.HibernateProvider;
import com.google.common.collect.ImmutableList;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import io.dropwizard.db.DataSourceFactory;
import org.hibernate.SessionFactory;

import java.util.Map;

import static com.et.secrets.SecretsModule.SECRETS_MANAGER_SECRET;
import static com.et.contacts.v2.data.contact.hibernate.dao.SessionFactoryName.CONTACTS_SESSION_FACTORY;

public class ContactsHibernateModule extends AbstractModule {

    private static final String CONTACTS_DATA_SOURCE_FACTORY = "ContactsDataSourceFactory";
    private static final String CONTACTS_HIBERNATE_CREDS = "ContactsHibernateCreds";
    private static final String CONTACTS_HIBERNATE = "ContactsHibernate";

    @Override
    protected void configure() {
    }

    @Provides
    @Singleton
    @Named(CONTACTS_DATA_SOURCE_FACTORY)
    DataSourceFactory getDataSourceFactory(ContactsStormConfiguration contactsStormConf) {
        return contactsStormConf.getContactsDataSourceFactory();
    }

    @Provides
    @Singleton
    @Named(CONTACTS_HIBERNATE_CREDS)
    HibernateCredentials buildHibernateCreds(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) {
        String user = secrets.get("com.et.contacts.db.user");
        String pass = secrets.get("com.et.contacts.db.pass");
        String url = "jdbc:mysql://" + secrets.get("com.et.contacts.db.host") + ":3306/contacts";

        return new HibernateCredentials(user, pass, url);
    }

    @Provides
    @Singleton
    @Named(CONTACTS_HIBERNATE)
    HibernateProvider contactsHibernate(@Named(CONTACTS_HIBERNATE_CREDS) HibernateCredentials contactsHibernateCreds,
            @Named(CONTACTS_DATA_SOURCE_FACTORY) DataSourceFactory contactsDsFactory, MetricRegistry metricRegistry) {
        return new HibernateProvider(contactsHibernateCreds, contactsDsFactory, metricRegistry,
                ImmutableList.of("com.et.contacts.v2.data.contact.hibernate.entity"));
    }

    @Provides
    @Singleton
    @Named(CONTACTS_SESSION_FACTORY)
    SessionFactory contactsSessionFactory(@Named(CONTACTS_HIBERNATE) HibernateProvider hibernateProvider) {
        return hibernateProvider.get();
    }
}
