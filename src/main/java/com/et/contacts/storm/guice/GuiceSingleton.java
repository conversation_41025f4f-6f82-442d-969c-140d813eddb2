package com.et.contacts.storm.guice;

import com.et.auth.client.AuthClientVaultModule;
import com.et.cassandra.guice.CassandraUtilsResourceModule;
import com.et.config.property.SystemPropertyModule;
import com.et.contacts.client.ContactsClientModule;
import com.et.contacts.storm.email.EmailResourceModule;
import com.et.contacts.v1.data.guice.DataTypeSerializerModule;
import com.et.dna.client.DNAClientModule;
import com.et.dna.client.NewDNAClientModule;
import com.et.ems.client.EMSClientModule;
import com.et.exhale.http.HttpClientModule;
import com.et.pagerduty.PagerDutyClientModule;
import com.et.search.client.transportclient.ETTransportClientModule;
import com.google.common.base.Optional;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;
import com.et.secrets.SecretsModule;

public class GuiceSingleton {
	private final static GuiceSingleton INSTANCE = new GuiceSingleton();
	private final Injector i;

	private GuiceSingleton() {
		i = Guice.createInjector(new ResourceModule(), new EmailResourceModule(), new DNAClientModule(),
				new HttpClientModule(), new ContactsClientModule(), new AuthClientVaultModule(),
				new SecretsModule(), new EMSClientModule(),
				new PagerDutyClientModule(), new SystemPropertyModule(), new DataTypeSerializerModule(),
				new CassandraUtilsResourceModule(), new ETTransportClientModule(),
				new NewDNAClientModule(),
				new ContactsHibernateModule());
	}

	public static GuiceSingleton get() {
		return INSTANCE;
	}

	public <T> T getInstance(Class<T> type) {
		return i.getInstance(type);
	}

	public <T> T getInstanceWithName(Class<T> type, String name) {
		return i.getInstance(Key.get(type, Names.named(name)));
	}

	public <T> T getInstanceWithName(TypeLiteral<T> tl, String name) {
		return i.getInstance(Key.get(tl, Names.named(name)));
	}

	public <T> Optional<T> getOptionalInstance(Class<T> type) {
		return Optional.of(i.getInstance(type));
	}
}
