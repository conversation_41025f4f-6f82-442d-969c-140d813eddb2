package com.et.contacts.storm.guice;

import static org.coursera.metrics.datadog.DatadogReporter.Expansion.COUNT;
import static org.coursera.metrics.datadog.DatadogReporter.Expansion.MEDIAN;
import static org.coursera.metrics.datadog.DatadogReporter.Expansion.P95;
import static org.coursera.metrics.datadog.DatadogReporter.Expansion.P99;
import static org.coursera.metrics.datadog.DatadogReporter.Expansion.RATE_15_MINUTE;
import static org.coursera.metrics.datadog.DatadogReporter.Expansion.RATE_1_MINUTE;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.sql.DataSource;

import org.apache.commons.dbutils.QueryRunner;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.Serializer;
import org.coursera.metrics.datadog.DatadogReporter;
import org.coursera.metrics.datadog.DatadogReporter.Expansion;
import org.coursera.metrics.datadog.transport.HttpTransport;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.sqs.AmazonSQSClient;
import com.codahale.metrics.MetricRegistry;
import com.datastax.driver.core.Cluster;
import com.datastax.driver.core.ConsistencyLevel;
import com.datastax.driver.core.PreparedStatement;
import com.datastax.driver.core.Session;
import com.et.auth.client.AuthClient;
import com.et.contacts.storm.dw.ContactsStormConfiguration;
import com.et.contacts.storm.utils.CensusUtils;
import com.et.contacts.storm.utils.GeocodeUtils;
import com.et.contacts.v1.data.guice.DateTimeFormatterModule;
import com.et.dna.client.DNAClient;
import com.et.dna.client.DNAServiceException;
import com.et.webcommon.auth.AuthApiCreds;
import com.et.webcommon.auth.AuthAppCreds;
import com.et.webcommon.db.DataSourceCreator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.google.common.base.Optional;
import com.google.common.base.Throwables;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.mchange.v2.c3p0.ComboPooledDataSource;

import net.spy.memcached.AddrUtil;
import net.spy.memcached.MemcachedClient;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import static com.et.secrets.SecretsModule.SECRETS_MANAGER_SECRET;

@SuppressWarnings("unused")
public class ResourceModule extends AbstractModule {

	@Override
	protected void configure() {
		install(new DateTimeFormatterModule());
	}

    @Provides
    ComboPooledDataSource comboPooledDataSource(@Named("com.et.env") String env) {
        ComboPooledDataSource comboPooledDataSource = new ComboPooledDataSource();
        if (!env.equals("prod")) {
            comboPooledDataSource.setMaxPoolSize(5);
        }
        return comboPooledDataSource;
    }

	@Provides
	@Singleton
	KafkaProducer<byte[], byte[]> buildKafkaProducer(
			@Named("com.et.kafka.brokers") String kafkaBrokers
	) {
		String clientID = "contacts";
		Map<String, Object> kafkaConfig = Maps.newHashMap();
		kafkaConfig.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBrokers);
		//Ideally we could set this dynamically in case our replication factor changes in the future
		//note: the brokers have min.insync.replicas=2
		kafkaConfig.put(ProducerConfig.ACKS_CONFIG, "-1");
		kafkaConfig.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, 5 * 1024 * 1024);
		kafkaConfig.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "gzip");
		kafkaConfig.put(ProducerConfig.CLIENT_ID_CONFIG, clientID);
		kafkaConfig.put(ProducerConfig.BLOCK_ON_BUFFER_FULL_CONFIG, true);
		kafkaConfig.put(ProducerConfig.RETRIES_CONFIG, 2);
		kafkaConfig.put(ProducerConfig.TIMEOUT_CONFIG, 3000);

		Serializer<byte[]> kafkaByteSerializer = new ByteArraySerializer();

		return new KafkaProducer(kafkaConfig, kafkaByteSerializer, kafkaByteSerializer);
	}

	@Provides
	@Singleton
	DataSource buildDataSource(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets, ComboPooledDataSource comboPooledDataSource) {
		return DataSourceCreator.createFromVaultNamespace("contacts", comboPooledDataSource, secrets);
	}

	@Provides
	@Singleton
	@Named("BatchSodasDbPool")
	DataSource buildBatchDataSource(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets, ComboPooledDataSource comboPooledDataSource) throws IOException {
		return DataSourceCreator.createFromVaultNamespace("sodas-batch", comboPooledDataSource, secrets);
	}

	@Provides
	@Named("SodasBatchConn")
	Connection getBatchConnection(@Named("BatchSodasDbPool") DataSource batchDbDataSource) throws SQLException {
		Connection conn = batchDbDataSource.getConnection();
		return conn;
	}

	@Provides
	@Singleton
	@Named("CensusDataSource")
	DataSource buildCensusDataSource(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets, ComboPooledDataSource comboPooledDataSource) {
		return DataSourceCreator.createFromVaultNamespace("census", comboPooledDataSource, secrets);
	}

	@Provides
	@Named("CensusQueryRunner")
	QueryRunner censusQueryRunner(@Named("CensusDataSource") DataSource ds) {
		return new QueryRunner(ds);
	}

	@Provides
	@Singleton
	@Named("OrgSlugCache")
	LoadingCache<Integer, Optional<String>> orgSlugCache(final AuthClient authClient, @Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) {
		return CacheBuilder.newBuilder().maximumSize(300).expireAfterWrite(1, TimeUnit.HOURS)
				.build(new CacheLoader<Integer, Optional<String>>() {
					@Override
					public Optional<String> load(Integer oid) throws Exception {
						return authClient.getOrgSlug(oid, AuthApiCreds.newAppCreds(secrets));
					}
				});
	}

	@Provides
	@Singleton
	@Named("SkipGateCache")
	LoadingCache<Integer, Boolean> skipGateCache(final DNAClient dnaClient, @Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) {
		return CacheBuilder.newBuilder().maximumSize(1000).expireAfterWrite(1, TimeUnit.MINUTES)
				.build(new CacheLoader<Integer, Boolean>() {

					@Override
					public Boolean load(Integer oid) throws Exception {
						try {
							return dnaClient.getDNAForOrgByKey(oid, AuthApiCreds.newAppCreds(secrets),
									"ET.Contacts.Storm.SkipLegacySync").asBoolean();
						} catch (DNAServiceException e) {
							// handle 400 error code which indicates oid does not exist in legacy
							if (e.getStatusCode() == 400) {
								return true;
							}

							throw e;
						}
					}

				});
	}

	@Provides
	@Singleton
	@Named("com.et.es.hosts")
	List<String> esHosts() {
		String hosts = System.getProperty("com.et.es.hosts");
		return Lists.newArrayList(hosts.split(","));
	}

	@Provides
	@Singleton
	MetricRegistry metricsReg(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets, @Named("com.et.env") String env) throws Exception {

		MetricRegistry registry = new MetricRegistry();
		if(env.equals("prod")){
			EnumSet<Expansion> expansions = EnumSet.of(COUNT, RATE_1_MINUTE, RATE_15_MINUTE, MEDIAN, P95, P99);
			HttpTransport httpTransport = new HttpTransport.Builder().withApiKey(secrets.get("com.et.datadog.api.key")).build();
			DatadogReporter datadogReporter = DatadogReporter.forRegistry(registry)
					.withEC2Host()
					.withTransport(httpTransport)
					.withExpansions(expansions)
					.build();
			datadogReporter.start(1, TimeUnit.MINUTES);
		}

		return registry;
	}

	@Singleton
	@Provides
	JedisPool jedisPool(@Named("com.et.contacts.storm.redis.host") String redisHost) {
		return new JedisPool(new JedisPoolConfig(), redisHost);
	}

	@Provides
	@Singleton
	AmazonSQSClient buildAmazonSQSClient() {
		AWSCredentials credentials = new BasicAWSCredentials("********************",
				"QN+nV+p2GSgIAIH1W36xr3oSFIKq/cTmtB2QmU8e");
		return new AmazonSQSClient(credentials);
	}

	@Provides
	@Singleton
	@Named ("KafkaProducer")
	KafkaProducer<byte[], byte[]> kafkaProducer(
			@Named("com.et.kafka.brokers") String kafkaBrokers) {

		String clientID = "spark";
		Map<String, Object> kafkaConfig = Maps.newHashMap();
		kafkaConfig.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBrokers);
		//Ideally we could set this dynamically in case our replication factor changes in the future
		//note: the brokers have min.insync.replicas=2
		kafkaConfig.put(ProducerConfig.ACKS_CONFIG, "-1");
		kafkaConfig.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, 5 * 1024 * 1024);
		kafkaConfig.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "gzip");
		kafkaConfig.put(ProducerConfig.CLIENT_ID_CONFIG, clientID);
		//probably want to set this to true once we have confidence in kafka and before it becomes our primary queue
		kafkaConfig.put(ProducerConfig.BLOCK_ON_BUFFER_FULL_CONFIG, true);
		kafkaConfig.put(ProducerConfig.RETRIES_CONFIG, 2);

		Serializer<byte[]> kafkaByteSerializer = new ByteArraySerializer();

		return new KafkaProducer(kafkaConfig, kafkaByteSerializer, kafkaByteSerializer);
	}

	@Provides
	@Singleton
	MemcachedClient memcachedClient() {
		try {
			return new MemcachedClient(AddrUtil.getAddresses(System.getProperty("com.et.memcache.host",
					"NO_HOST_SPECIFIED")));
		} catch (IOException e) {
			throw Throwables.propagate(e);
		}
	}

	@Provides
	@Singleton
	@Named("ContactsKsSession")
	Session contactSession(Cluster cluster, @Named("com.et.contacts.cas.contacts_keyspace") String keyspace) {
		return cluster.connect(keyspace);
	}

	@Provides
	@Singleton
	@Named("ContactsIndexKsSession")
	Session oidIndexSession(Cluster cluster, @Named("com.et.contacts.cas.contacts_index_keyspace") String keyspace) {
		return cluster.connect(keyspace);
	}

	@Provides
	@Singleton
	@Named("SelectContactMetaRow")
	PreparedStatement selectContactIdentities(@Named("ContactsKsSession") Session session) {
		return session.prepare(
				"SELECT * FROM contact WHERE id = ? AND property_id = 0")
				.setConsistencyLevel(ConsistencyLevel.QUORUM);
	}

	@Provides
	@Singleton
	@Named("SelectIdentity")
	PreparedStatement selectIdentity(@Named("ContactsKsSession") Session session) {
		return session.prepare(
				"SELECT * FROM identity WHERE value = ? AND oid = ? AND type = ?")
				.setConsistencyLevel(ConsistencyLevel.QUORUM);
	}

	@Provides
	@Singleton
	@Named("DeleteIdentity")
	PreparedStatement deleteIdentity(@Named("ContactsKsSession") Session session) {
		return session.prepare(
				"DELETE FROM identity WHERE value = ? AND oid = ? AND type = ?")
				.setConsistencyLevel(ConsistencyLevel.QUORUM);
	}

	@Provides
	@Singleton
	@Named("AddIdentityToContact")
	PreparedStatement addIdentityToContact(@Named("ContactsKsSession") Session session) {
		return session.prepare(
				"UPDATE contact SET updated_at = ?, identities = identities + ? WHERE property_id = 0 AND id = ?")
				.setConsistencyLevel(ConsistencyLevel.QUORUM);
	}

	@Provides
	@Singleton
	@Named("UpdateContactUpdatedAt")
	PreparedStatement updateContactUpdatedAt(@Named("ContactsKsSession") Session session) {
		return session.prepare(
				"UPDATE contact SET updated_at = ? WHERE property_id = 0 AND id = ?")
				.setConsistencyLevel(ConsistencyLevel.QUORUM);
	}

    @Provides
    @Singleton
    @Named("GeocodesKsSession")
    Session geocodeSession(Cluster cluster, @Named("com.et.contacts.cas.geocodes_keyspace") String keyspace) {
        return cluster.connect(keyspace);
    }

	@Provides
	@Singleton
	GeocodeUtils geocodeUtils() {
		return new GeocodeUtils();
	}

	@Provides
	@Singleton
	CensusUtils censusUtils() {
		return new CensusUtils();
	}

	@Provides
	@Named("com.et.sendgrid.user")
	String getSendgridUser(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) {
	    return secrets.get("com.et.sendgrid.user");
	}

	@Provides
	@Named("com.et.sendgrid.pass")
	String getSendgridPass(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) {
	    return secrets.get("com.et.sendgrid.pass");
	}

	@Provides
	@Named("com.et.hostedgraphite.api.key")
	String getHostedGraphiteApiKey(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) {
	    return secrets.get("com.et.hostedgraphite.api.key");
	}

	@Provides
	@Named("com.et.auth.super_user_token")
	String getSuperUserAuth(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) {
	    return secrets.get("com.et.auth.super_user_token");
	}

	@Provides
	@Named("com.et.auth.app_key")
	String getAppKey(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) {
	    return secrets.get("com.et.auth.app_key");
	}

	@Provides
	@Named("auth.app_key")
	String getAppKeyShortened(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) {
	    return secrets.get("com.et.auth.app_key");
	}

	@Provides
	@Singleton
	public AuthAppCreds getAuthAppCreds(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) {
	    String auth = secrets.get("com.et.auth.super_user_token");
		String authProvider = "EvertrueAppToken";
		String applicationKey = secrets.get("com.et.auth.app_key");
		return new AuthAppCreds(auth, authProvider, applicationKey);
	}

	@Provides
	@Singleton
	AuthApiCreds buildAuthApiCreds(@Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) throws IOException {
		return AuthApiCreds.newAppCreds(secrets);
	}

	@Provides
	@Singleton
	ContactsStormConfiguration getDropWizardConfiguration() throws IOException {
		return new ObjectMapper(new YAMLFactory())
				.readValue(getClass().getResourceAsStream("/contacts_storm.yml"), ContactsStormConfiguration.class);
	}
}
