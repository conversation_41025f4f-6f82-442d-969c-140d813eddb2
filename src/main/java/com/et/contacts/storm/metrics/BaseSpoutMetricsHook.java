package com.et.contacts.storm.metrics;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

import org.apache.log4j.Logger;

import backtype.storm.hooks.info.SpoutAckInfo;
import backtype.storm.hooks.info.SpoutFailInfo;

import com.codahale.metrics.Meter;
import com.et.pagerduty.Incident;

public class BaseSpoutMetricsHook extends BaseMetricsHook {
	private static final Logger log = Logger.getLogger(BaseSpoutMetricsHook.class);
	private static AtomicLong sentToPagerDutyTimeStamp = new AtomicLong();
	private static AtomicBoolean alertSent = new AtomicBoolean();

	public BaseSpoutMetricsHook(String componentName) {
		super(componentName);
		registerGaugeWithResetableCounter("ack_count");
		registerGaugeWithResetableCounter("fail_count");
	}

	@Override
	public void spoutAck(SpoutAckInfo info) {
		incResetableCounter("ack_count");
		if (info.completeLatencyMs != null) {
			metrics.meter(buildMetricName() + "_complete_latency").mark(info.completeLatencyMs);
		}
	}

	@Override
	public void spoutFail(SpoutFailInfo info) {
		incResetableCounter("fail_count");
		if (info.failLatencyMs != null) {
			metrics.meter(buildMetricName() + "_fail_latency").mark(info.failLatencyMs);
		}

		Meter meter = metrics.meter(buildMetricName() + "_ema_fail_count_ignore_metric");
		meter.mark();

		maybeSendEventToPagerDuty(meter);
	}

	private void maybeSendEventToPagerDuty(Meter meter) {
		log.info("15m error rate: " + meter.getFifteenMinuteRate());
		if (meter.getFifteenMinuteRate() < 25) {
			return;
		}

		long lastTimestamp = sentToPagerDutyTimeStamp.get();
		if ((System.currentTimeMillis() - lastTimestamp) < TimeUnit.MILLISECONDS.convert(15, TimeUnit.MINUTES)) {
			return;
		}

		if (sentToPagerDutyTimeStamp.compareAndSet(lastTimestamp, System.currentTimeMillis())) {
			triggerEventInPagerDuty(lastTimestamp);
		}
	}

	private void triggerEventInPagerDuty(long lastTimestamp) {
		Incident incident = buildIncident("Spout error rate is >= 25%");

		boolean success = pdClient.trigger(incident);
		resetTimestampIfNotSuccess(lastTimestamp, success);
	}

	private Incident buildIncident(String desc) {
		return Incident.newBuilder().setServiceKey("25cb02c5b1444a1a8b0ee9792ae4a057")
				.setIncidentKey("contacts_topology").setDescription(desc).build();
	}

	private void resetTimestampIfNotSuccess(long lastTimestamp, boolean success) {
		if (!success) {
			log.warn("Couldn't send event to pager duty");
			sentToPagerDutyTimeStamp.set(lastTimestamp);
		} else {
			alertSent.compareAndSet(false, true);
		}
	}
}