package com.et.contacts.storm.metrics;

import java.util.Map;

import org.apache.log4j.Logger;

import backtype.storm.hooks.ITaskHook;
import backtype.storm.hooks.info.BoltAckInfo;
import backtype.storm.hooks.info.BoltExecuteInfo;
import backtype.storm.hooks.info.BoltFailInfo;
import backtype.storm.hooks.info.EmitInfo;
import backtype.storm.hooks.info.SpoutAckInfo;
import backtype.storm.hooks.info.SpoutFailInfo;
import backtype.storm.task.TopologyContext;

import com.codahale.metrics.Counter;
import com.codahale.metrics.Gauge;
import com.codahale.metrics.MetricRegistry;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.pagerduty.PagerDutyClient;

public abstract class BaseMetricsHook implements ITaskHook {
	private static final Logger log = Logger.getLogger(BaseMetricsHook.class);
	protected final MetricRegistry metrics;
	private final String componentName;
	protected PagerDutyClient pdClient;

	public BaseMetricsHook(String componentName) {
		this.metrics = GuiceSingleton.get().getInstance(MetricRegistry.class);
		this.pdClient = GuiceSingleton.get().getInstance(PagerDutyClient.class);
		this.componentName = componentName;
	}

	protected String buildMetricName() {
		return "storm_topology." + componentName;
	}

	protected void registerGaugeWithResetableCounter(final String name) {
		try {
			metrics.register(buildFullMetricName(name), new Gauge<Long>() {
				@Override
				public Long getValue() {
					Counter c = metrics.counter(buildIgnoreMetricName(name));
					long count = c.getCount();
					c.dec(count);
					return count;
				}
			});
		} catch (IllegalArgumentException e) {
			//don't care
		}
	}

	private String buildFullMetricName(final String name) {
		return buildMetricName() + "_" + name;
	}

	private String buildIgnoreMetricName(final String name) {
		return buildMetricName() + "_" + name + "_ignore_metric";
	}

	protected void incResetableCounter(String name) {
		metrics.counter(buildIgnoreMetricName(name)).inc();
	}

	@Override
	public void spoutAck(SpoutAckInfo info) {
	}

	@Override
	public void spoutFail(SpoutFailInfo info) {
	}

	@Override
	public void boltAck(BoltAckInfo info) {
	}

	@Override
	public void boltFail(BoltFailInfo info) {
	}

	@Override
	public void boltExecute(BoltExecuteInfo info) {
	}

	@Override
	public void prepare(Map conf, TopologyContext context) {
	}

	@Override
	public void cleanup() {
	}

	@Override
	public void emit(EmitInfo info) {
	}
}