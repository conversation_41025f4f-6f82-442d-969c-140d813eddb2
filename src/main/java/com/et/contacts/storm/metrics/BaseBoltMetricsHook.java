package com.et.contacts.storm.metrics;

import backtype.storm.hooks.info.BoltAckInfo;
import backtype.storm.hooks.info.BoltExecuteInfo;
import backtype.storm.hooks.info.BoltFailInfo;

public class BaseBoltMetricsHook extends BaseMetricsHook {

	public BaseBoltMetricsHook(String componentName) {
		super(componentName);
		registerGaugeWithResetableCounter("ack_count");
		registerGaugeWithResetableCounter("fail_count");
	}

	@Override
	public void boltAck(BoltAckInfo info) {
		incResetableCounter("ack_count");
	}

	@Override
	public void boltFail(BoltFailInfo info) {
		incResetableCounter("fail_count");
	}

	@Override
	public void boltExecute(BoltExecuteInfo info) {
		if (info.executeLatencyMs != null) {
			metrics.meter(buildMetricName() + "_exec_latency").mark(info.executeLatencyMs);
		}
	}
}
