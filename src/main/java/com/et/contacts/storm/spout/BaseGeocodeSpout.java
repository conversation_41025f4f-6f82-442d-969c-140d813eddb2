package com.et.contacts.storm.spout;

import java.io.IOException;

import org.apache.log4j.Logger;
import org.xerial.snappy.Snappy;

import backtype.storm.tuple.Values;
import backtype.storm.utils.Utils;

import com.amazonaws.services.sqs.model.Message;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.google.common.io.BaseEncoding;
import com.google.protobuf.InvalidProtocolBufferException;

public abstract class BaseGeocodeSpout extends BaseContactSpout {
	private final static Logger log = Logger.getLogger(BaseGeocodeSpout.class);

	public BaseGeocodeSpout(String queueUrl) {
		super(queueUrl);
	}

	@Override
	public void nextTuple() {
		if (queue.isEmpty()) {
			ReceiveMessageResult receiveMessageResult = sqs.receiveMessage(new ReceiveMessageRequest(queueUrl)
					.withMaxNumberOfMessages(10));
			queue.addAll(receiveMessageResult.getMessages());
		}

		Message message = queue.poll();

		if (message != null) {
			try {
				byte[] compressedContactBytes = BaseEncoding.base64().decode(message.getBody());
				byte[] contactBytes = Snappy.uncompress(compressedContactBytes);

				Contact contactWithAddressesToGeocode = Contact.parseFrom(contactBytes);
				collector.emit(new Values(contactWithAddressesToGeocode), message.getReceiptHandle());

			} catch (IllegalArgumentException | IOException e) {
				collector.reportError(e);
			}
		} else {
			Utils.sleep(getSleepTime());
		}
	}

	@Override
	public int getSleepTime() {
		return 2000;
	}

}
