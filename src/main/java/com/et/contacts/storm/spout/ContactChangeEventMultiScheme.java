package com.et.contacts.storm.spout;

import static backtype.storm.utils.Utils.tuple;
import static java.util.Arrays.asList;

import java.util.List;
import java.util.concurrent.ExecutionException;

import org.apache.log4j.Logger;

import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.v1.data.ContactsProtoBuf;
import com.et.contacts.v1.data.NestedPropertyMap;
import com.et.contacts.v1.data.ProtoUtils;
import com.google.common.cache.LoadingCache;
import com.google.inject.TypeLiteral;
import com.google.protobuf.InvalidProtocolBufferException;

import backtype.storm.spout.MultiScheme;
import backtype.storm.tuple.Fields;

/**
 * Created by tomnorden on 7/8/15.
 */
public class ContactChangeEventMultiScheme implements MultiScheme {
    private final static Logger log = Logger.getLogger(ContactChangeEventMultiScheme.class);
    protected LoadingCache<Integer, NestedPropertyMap> propMapCache = null;

    @Override
    public Iterable<List<Object>> deserialize(byte[] ser) {
        try {
            ContactsProtoBuf.ContactChangeEvent eventWithoutPropMeta = ContactsProtoBuf.ContactChangeEvent.parseFrom(ser);
            ContactsProtoBuf.ContactChangeEvent event = bindPropertyMetaFields(eventWithoutPropMeta);
            long contactId = event.getCurrent().getId();
            return asList(tuple(contactId, event));
        } catch (InvalidProtocolBufferException e) {
            log.error("unable to deserialize event", e);
        } catch (ExecutionException e) {
            log.error("unable to bind property meta fields", e);
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public Fields getOutputFields() {
        return new Fields("contact_id", "event");
    }

    protected ContactsProtoBuf.ContactChangeEvent bindPropertyMetaFields(ContactsProtoBuf.ContactChangeEvent eventWithoutPropMeta) throws ExecutionException {
        if (propMapCache == null) {
            propMapCache = GuiceSingleton.get().getInstanceWithName(
                    new TypeLiteral<LoadingCache<Integer, NestedPropertyMap>>() {
                    }, "ContactsPropMapCache");
        }
        ContactsProtoBuf.Contact current = eventWithoutPropMeta.getCurrent();
        ContactsProtoBuf.Contact previous = eventWithoutPropMeta.getPrevious();
        int oid = eventWithoutPropMeta.getOid();

        NestedPropertyMap propMap = null;
        while (propMap == null) {
            try {
                propMap = propMapCache.get(oid);
                break;
            } catch (Exception e) {
                log.error(String.format("failed to get property map for oid: %d", oid), e);
            }
        }

        ContactsProtoBuf.ContactChangeEvent.Builder eventBldr = eventWithoutPropMeta.toBuilder();
        eventBldr.setCurrent(ProtoUtils.bindFieldsAndSetMeta(current, propMap));
        eventBldr.setPrevious(ProtoUtils.bindFieldsAndSetMeta(previous, propMap));

        return eventBldr.build();
    }

}
