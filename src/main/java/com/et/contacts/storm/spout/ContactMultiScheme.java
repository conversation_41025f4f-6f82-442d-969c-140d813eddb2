package com.et.contacts.storm.spout;

import static backtype.storm.utils.Utils.tuple;
import static java.util.Arrays.asList;

import java.util.List;

import org.apache.log4j.Logger;

import com.et.contacts.v1.data.ContactsProtoBuf;
import com.google.protobuf.InvalidProtocolBufferException;

import backtype.storm.spout.MultiScheme;
import backtype.storm.tuple.Fields;

/**
 * Created by to<PERSON><PERSON><PERSON> on 7/30/15.
 */
public class ContactMultiScheme implements MultiScheme {
    private final static Logger log = Logger.getLogger(ContactMultiScheme.class);
    @Override
    public Iterable<List<Object>> deserialize(byte[] ser) {
        ContactsProtoBuf.Contact contactWithAddressesToGeocode = null;
        try {
            contactWithAddressesToGeocode = ContactsProtoBuf.Contact.parseFrom(ser);
            return asList(tuple(contactWithAddressesToGeocode.getId(), contactWithAddressesToGeocode));
        } catch (InvalidProtocolBufferException e) {
            log.error("unable to deserialize event", e);
        }
        return null;
    }

    @Override
    public Fields getOutputFields() {
        return new Fields("contact_id", "contact-with-addresses-to-change-geocode");
    }
}
