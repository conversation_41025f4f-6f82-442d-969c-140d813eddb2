package com.et.contacts.storm.spout;

import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;

import org.apache.log4j.Logger;
import org.xerial.snappy.Snappy;

import backtype.storm.spout.SpoutOutputCollector;
import backtype.storm.task.TopologyContext;
import backtype.storm.topology.base.BaseRichSpout;
import backtype.storm.tuple.Values;
import backtype.storm.utils.Utils;

import com.amazonaws.AmazonClientException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.sqs.AmazonSQSAsync;
import com.amazonaws.services.sqs.AmazonSQSAsyncClient;
import com.amazonaws.services.sqs.model.DeleteMessageRequest;
import com.amazonaws.services.sqs.model.Message;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.storm.metrics.BaseSpoutMetricsHook;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactChangeEvent;
import com.et.contacts.v1.data.NestedPropertyMap;
import com.et.contacts.v1.data.ProtoUtils;
import com.et.pagerduty.PagerDutyClient;
import com.google.common.cache.LoadingCache;
import com.google.common.io.BaseEncoding;
import com.google.inject.TypeLiteral;
import com.google.protobuf.InvalidProtocolBufferException;

@SuppressWarnings("serial")
public abstract class BaseContactSpout extends BaseRichSpout {
	private final static Logger log = Logger.getLogger(BaseContactSpout.class);
	protected SpoutOutputCollector collector;
	protected AmazonSQSAsync sqs;
	protected final String queueUrl;
	protected LinkedBlockingQueue<Message> queue;
	private PagerDutyClient pdClient;
	protected LoadingCache<Integer, NestedPropertyMap> propMapCache;

	public BaseContactSpout(String queueUrl) {
		this.queueUrl = queueUrl;
	}

	@Override
	public void open(Map conf, TopologyContext context, SpoutOutputCollector collector) {
		this.collector = collector;

		queue = new LinkedBlockingQueue<Message>();

		AWSCredentials credentials = new BasicAWSCredentials("********************",
				"QN+nV+p2GSgIAIH1W36xr3oSFIKq/cTmtB2QmU8e");

		sqs = new AmazonSQSAsyncClient(credentials);
		propMapCache = GuiceSingleton.get().getInstanceWithName(
				new TypeLiteral<LoadingCache<Integer, NestedPropertyMap>>() {
				}, "ContactsPropMapCache");
	}

	@Override
	public void nextTuple() {
		try {
			if (queue.isEmpty()) {
				ReceiveMessageResult receiveMessageResult = sqs.receiveMessage(new ReceiveMessageRequest(queueUrl)
						.withMaxNumberOfMessages(10));
				queue.addAll(receiveMessageResult.getMessages());
			}

			Message message = queue.poll();

			if (message != null) {
				try {
					byte[] compressedContactBytes = BaseEncoding.base64().decode(message.getBody());
					byte[] contactBytes = Snappy.uncompress(compressedContactBytes);

					ContactChangeEvent eventWithoutPropMeta = ContactChangeEvent.parseFrom(contactBytes);
					ContactChangeEvent event = bindPropertyMetaFields(eventWithoutPropMeta);
					long contactId = event.getCurrent().getId();

					collector.emit(new Values(contactId, event), message.getReceiptHandle());
				} catch (InvalidProtocolBufferException | IllegalArgumentException e) {
					collector.reportError(e);
				}
			} else {
				Utils.sleep(getSleepTime());
			}
		} catch (Exception e) {
			log.error(e, e);
			collector.reportError(e);
		}
	}

	protected ContactChangeEvent bindPropertyMetaFields(ContactChangeEvent eventWithoutPropMeta) throws ExecutionException {
		Contact current = eventWithoutPropMeta.getCurrent();
		Contact previous = eventWithoutPropMeta.getPrevious();
		int oid = eventWithoutPropMeta.getOid();
		NestedPropertyMap propMap = propMapCache.get(oid);

		ContactChangeEvent.Builder eventBldr = eventWithoutPropMeta.toBuilder();
		eventBldr.setCurrent(ProtoUtils.bindFieldsAndSetMeta(current, propMap));
		eventBldr.setPrevious(ProtoUtils.bindFieldsAndSetMeta(previous, propMap));

		return eventBldr.build();
	}

	@Override
	public void ack(Object msgId) {
		deleteSQSMessage(msgId);
	}

	protected void deleteSQSMessage(Object msgId) {
		try {
			sqs.deleteMessageAsync(new DeleteMessageRequest(queueUrl, (String) msgId));
		} catch (AmazonClientException ace) {
		}
	}

	@Override
	public void fail(Object msgId) {
	}

	@Override
	public void close() {
		sqs.shutdown();
		((AmazonSQSAsyncClient) sqs).getExecutorService().shutdownNow();
	}

	public String getStreamId(Message message) {
		return Utils.DEFAULT_STREAM_ID;
	}

	public abstract int getSleepTime();

	public abstract String getSpoutName();

}
