package com.et.contacts.storm.spout;

import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

import org.apache.log4j.Logger;

import backtype.storm.spout.SpoutOutputCollector;
import backtype.storm.task.TopologyContext;
import backtype.storm.topology.OutputFieldsDeclarer;
import backtype.storm.topology.ReportedFailedException;
import backtype.storm.topology.base.BaseRichSpout;
import backtype.storm.tuple.Fields;
import backtype.storm.tuple.Values;
import backtype.storm.utils.Utils;

import com.amazonaws.AmazonClientException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.sqs.AmazonSQSAsync;
import com.amazonaws.services.sqs.AmazonSQSAsyncClient;
import com.amazonaws.services.sqs.model.DeleteMessageRequest;
import com.amazonaws.services.sqs.model.Message;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageResult;
import com.et.contacts.v1.data.ContactsProtoBuf.ContactListMembershipChangeEvent;
import com.google.common.io.BaseEncoding;
import com.google.protobuf.InvalidProtocolBufferException;

public class ListMembershipChangeSpout extends BaseRichSpout {
	private final static Logger LOG = Logger.getLogger(ListMembershipChangeSpout.class);

	private SpoutOutputCollector collector;
	private AmazonSQSAsync sqs;
	private String queueUrl;
	private LinkedBlockingQueue<Message> queue;

	public ListMembershipChangeSpout(String queueUrl) {
		this.queueUrl = queueUrl;
	}

	@Override
	public void open(Map conf, TopologyContext context, SpoutOutputCollector collector) {
		this.collector = collector;

		queue = new LinkedBlockingQueue<Message>();
		AWSCredentials credentials = new BasicAWSCredentials("********************",
				"QN+nV+p2GSgIAIH1W36xr3oSFIKq/cTmtB2QmU8e");

		sqs = new AmazonSQSAsyncClient(credentials);
	}

	@Override
	public void nextTuple() {
		try {
			if (queue.isEmpty()) {
				ReceiveMessageResult receiveMessageResult = sqs.receiveMessage(new ReceiveMessageRequest(queueUrl)
						.withMaxNumberOfMessages(10));
				queue.addAll(receiveMessageResult.getMessages());
			}

			Message message = queue.poll();

			if (message != null) {
				try {
					byte[] membershipChangeBytes = BaseEncoding.base64().decode(message.getBody());
					ContactListMembershipChangeEvent event = ContactListMembershipChangeEvent
							.parseFrom(membershipChangeBytes);
					collector.emit(new Values(event), message.getReceiptHandle());
				} catch (IllegalArgumentException | InvalidProtocolBufferException e) {
					collector.reportError(e);
				}
			} else {
				Utils.sleep(2000);
			}
		} catch (Exception e) {
			throw new ReportedFailedException(e);
		}
	}

	@Override
	public void ack(Object msgId) {
		try {
			sqs.deleteMessageAsync(new DeleteMessageRequest(queueUrl, (String) msgId));
		} catch (AmazonClientException ace) {
		}
	}

	@Override
	public void fail(Object msgId) {
		LOG.info("Failed msg id: " + msgId);
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields("event"));
	}

}
