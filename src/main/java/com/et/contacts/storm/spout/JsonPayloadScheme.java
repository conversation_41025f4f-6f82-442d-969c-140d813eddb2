package com.et.contacts.storm.spout;

import java.util.List;

import com.et.json.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;

import backtype.storm.spout.MultiScheme;
import backtype.storm.tuple.Fields;

import static backtype.storm.utils.Utils.tuple;
import static java.util.Arrays.asList;

public class JsonPayloadScheme implements MultiScheme {

	@Override
	public Iterable<List<Object>> deserialize(byte[] ser) {
		JsonNode payload = JsonUtils.fromString(new String(ser));
		long contactId = payload.get("contact_id").asLong();
		
		return asList(tuple(contactId, payload));
	}

	@Override
	public Fields getOutputFields() {
        return new Fields("contact_id", "event");
	}

}
