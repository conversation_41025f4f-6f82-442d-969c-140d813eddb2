package com.et.contacts.storm.dw;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.dropwizard.Configuration;
import io.dropwizard.db.DataSourceFactory;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

public class ContactsStormConfiguration {

    @Valid
    @NotNull
    @JsonProperty("contactsDatabase")
    private DataSourceFactory contactsDatabase = new DataSourceFactory();

    public DataSourceFactory getContactsDataSourceFactory() {
        return contactsDatabase;
    }
}
