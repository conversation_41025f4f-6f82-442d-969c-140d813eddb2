package com.et.contacts.storm.email;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;

import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.Mustache;
import com.github.mustachejava.MustacheFactory;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.google.inject.name.Names;

public class EmailResourceModule extends AbstractModule {

	@Override
	protected void configure() {
		bind(EmailRenderer.class).annotatedWith(Names.named("contacts.suggested_update"))
				.to(SuggestedUpdateEmail.class);
	}

	@Provides
	@Singleton
	@Named("SuggestedUpdateMustache")
	Mustache suggestedUpdate() {
		InputStream is = SuggestedUpdateEmail.class.getResourceAsStream("/email_templates/suggested_update.mustache");
		Reader reader = new InputStreamReader(is);

		MustacheFactory mf = new DefaultMustacheFactory();
		return mf.compile(reader, "suggested_update.mustache");
	}
}
