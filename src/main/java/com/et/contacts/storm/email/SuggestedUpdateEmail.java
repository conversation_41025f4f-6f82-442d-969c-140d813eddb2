package com.et.contacts.storm.email;

import java.io.StringWriter;
import java.util.List;
import java.util.Map;

import com.et.auth.client.AuthClient;
import com.et.contacts.client.ContactsClient;
import com.et.contacts.v1.data.ContactsProtoBuf.Contact;
import com.et.contacts.v1.data.ContactsProtoBuf.Identity;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObject;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObjectList;
import com.et.contacts.v1.data.ContactsProtoBuf.UpdateSource;
import com.et.contacts.v1.data.NestedPropertyMap;
import com.et.contacts.v1.data.PropertyUtils;
import com.et.user.data.UsersProtoBuf.Affiliation;
import com.et.user.data.UsersProtoBuf.User;
import com.et.webcommon.auth.AuthApiCreds;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.mustachejava.Mustache;
import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import static com.et.secrets.SecretsModule.SECRETS_MANAGER_SECRET;

public class SuggestedUpdateEmail implements EmailRenderer<JsonNode> {
	private Map<String, String> secrets;
	private AuthClient authClient;
	private ContactsClient contactsClient;
	private Mustache template;

	@Inject
	public SuggestedUpdateEmail(AuthClient authClient, ContactsClient contactsClient,
			@Named("SuggestedUpdateMustache") Mustache template, @Named(SECRETS_MANAGER_SECRET) Map<String, String> secrets) {
		this.secrets = secrets;
		this.authClient = authClient;
		this.contactsClient = contactsClient;
		this.template = template;
	}

	@Override
	public String renderTextBody(JsonNode msgDescriptor) {
		StringWriter sw = new StringWriter(1024);

		template.execute(sw, buildModel(msgDescriptor));

		return sw.toString();
	}

	@Override
	public String renderHtmlBody(JsonNode msgDescriptor) {
		return null;
	}

	public Map<String, Object> buildModel(JsonNode msgDescriptor) {
		JsonNode context = msgDescriptor.get("context");
		int oid = msgDescriptor.get("oid").asInt();
		// Get user from auth
		int userId = context.get("reporting_user_id").asInt();
		User user = authClient.getUserById(userId, oid, AuthApiCreds.newAppCreds(this.secrets));

		NestedPropertyMap propMap = new NestedPropertyMap(contactsClient.getAllPropertiesForOrg(oid,
				AuthApiCreds.newAppCreds(this.secrets)));

		Optional<Contact> maybeContactForUser = getContactForUser(user, oid, propMap);

		Contact targetContact = getContactForSuggestedUpdateTarget(context.get("target_contact_id").asLong(), oid,
				propMap);

		Map<String, Object> scopes = Maps.newHashMap();
		scopes.put("emails", getEmailsFromTargetContact(targetContact));
		scopes.put("firstName", PropertyUtils.getPropertyValue(targetContact, "name_first").orNull());
		scopes.put("lastName", PropertyUtils.getPropertyValue(targetContact, "name_last").orNull());
		scopes.put("year", PropertyUtils.getPropertyValue(targetContact, "year").orNull());
		scopes.put("remoteId", getRemoteIdIdentity(targetContact).orNull());
		scopes.put("contactId", targetContact.getId());
		scopes.put("suggestion", context.get("suggestion").asText());
		scopes.put("submitterName", user.getName());

		if (maybeContactForUser.isPresent()) {
			scopes.put("submitterYear", PropertyUtils.getPropertyValue(maybeContactForUser.get(), "year").orNull());
			scopes.put("submitterContactId", maybeContactForUser.get().getId());
		}

		return scopes;
	}

	private Optional<String> getRemoteIdIdentity(Contact contact) {
		for (Identity identity : contact.getIdentityList()) {
			if (identity.getType() == Identity.Type.REMOTE_ID) {
				return Optional.of(identity.getValue());
			}
		}
		return Optional.absent();
	}

	private List<String> getEmailsFromTargetContact(Contact contact) {
		List<String> emailAddresses = Lists.newArrayList();

		NameValueObjectList emails = PropertyUtils.getNvolByName(contact, "emails");
		if (emails == null) {
			return emailAddresses;
		}

		for (NameValueObject email : emails.getNameValueObjectList()) {
			Optional<String> emailAddress = PropertyUtils.getPropertyValue(email, "email");
			if (emailAddress.isPresent()) {
				emailAddresses.add(emailAddress.get());
			}
		}

		return emailAddresses;
	}

	private Contact getContactForSuggestedUpdateTarget(long contactId, int oid, NestedPropertyMap propMap) {
		return contactsClient.getContactById(oid, AuthApiCreds.newAppCreds(this.secrets), contactId,
				Optional.<UpdateSource> absent(), propMap);
	}

	private Optional<Contact> getContactForUser(User user, int oid, NestedPropertyMap propMap) {
		long contactId = getContactIdFromAffiliation(user, oid);

		if (contactId == 0) {
			return Optional.absent();
		}

		Contact contact = contactsClient.getContactById(oid, AuthApiCreds.newAppCreds(this.secrets), contactId,
				Optional.<UpdateSource> absent(), propMap);

		return Optional.of(contact);
	}

	private long getContactIdFromAffiliation(User user, int oid) {
		long contactId = 0;

		for (Affiliation affil : user.getAffiliationList()) {
			if (affil.getOrganization().getId() == oid) {
				contactId = affil.getContactId();
				break;
			}
		}

		return contactId;
	}
}