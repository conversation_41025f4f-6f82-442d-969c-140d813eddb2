package com.et.contacts.storm.utils;

import com.datastax.driver.core.PreparedStatement;
import com.datastax.driver.core.ResultSet;
import com.datastax.driver.core.Row;
import com.datastax.driver.core.Session;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObject;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValueObjectList;
import com.et.contacts.v1.data.ContactsProtoBuf.NameValuePair;
import com.et.contacts.v1.data.ContactsProtoBuf.Property;
import com.et.contacts.v1.data.ContactsProtoBuf.UpdateSource;
import com.et.contacts.v1.data.PropertyUtils;
import com.et.contacts.v1.data.property.NVPWithOptionalParent;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.CharMatcher;
import com.google.common.base.Charsets;
import com.google.common.base.Optional;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.google.common.collect.Range;
import com.google.common.collect.Sets;
import com.google.common.hash.Hashing;
import org.apache.log4j.Logger;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class GeocodeUtils {
	private static final Logger LOG = Logger.getLogger(GeocodeUtils.class);

	private final static Set<String> GEOCODE_PROPERTIES = ImmutableSet.of("lat",
                                                                          "lng",
                                                                          "house_value_lq",
                                                                          "house_value_median",
                                                                          "house_value_uq",
                                                                          "income_median");

	public Set<String> getGeocodeProperties() {
		return GEOCODE_PROPERTIES;
	}

	public NameValueObjectList buildAddressesFromChanges(List<NVPWithOptionalParent> nvpChanges) {
		Set<String> addressGuids = Sets.newHashSet();
		NameValueObjectList.Builder addressesBldr = NameValueObjectList.newBuilder().setProperty(
				Property.newBuilder().setName("addresses").build());
		for (NVPWithOptionalParent nvp : nvpChanges) {
			if (!isGeocodeProperty(nvp.getNvp().getProperty().getName()) && nvp.getNvo().isPresent()) {
				NameValueObject nvo = nvp.getNvo().get();
				if (nvo.getProperty().getName().equals("address") && !addressGuids.contains(nvo.getGuid())) {
					addressGuids.add(nvo.getGuid());
					addressesBldr.addNameValueObject(nvo);
				}
			}
		}
		return addressesBldr.build();
	}

	public boolean isGeocodeProperty(String propName) {
		return GEOCODE_PROPERTIES.contains(propName);
	}

	public boolean isAddressesEmpty(NameValueObjectList addresses) {
		for (NameValueObject nvo : addresses.getNameValueObjectList()) {
			if (nvo.getNameValuePairCount() > 0) {
				return false;
			}
		}
		return true;
	}

	public String getAddressString(final NameValueObject address, boolean retry) {
		String addressString = "";

		if (!retry) {
			Optional<String> maybeAddress1 = PropertyUtils.getPropertyValue(address, "address_1");
			if (maybeAddress1.isPresent()) {
				addressString += maybeAddress1.get();
			}

			Optional<String> maybeAddress2 = PropertyUtils.getPropertyValue(address, "address_2");
			if (maybeAddress2.isPresent()) {
				addressString += addressString.isEmpty() ? "" : " ";
				addressString += maybeAddress2.get();
			}

			Optional<String> maybeAddress3 = PropertyUtils.getPropertyValue(address, "address_3");
			if (maybeAddress3.isPresent()) {
				addressString += addressString.isEmpty() ? "" : " ";
				addressString += maybeAddress3.get();
			}
		}

		Optional<String> maybeCity = PropertyUtils.getPropertyValue(address, "city");
		if (maybeCity.isPresent()) {
			addressString += addressString.isEmpty() ? "" : " ";
			addressString += maybeCity.get();
		}

		Optional<String> maybeState = PropertyUtils.getPropertyValue(address, "state");
		if (maybeState.isPresent()) {
			addressString += addressString.isEmpty() ? "" : " ";
			addressString += maybeState.get();
		}

		if (!retry) {
			Optional<String> maybeZipCode = PropertyUtils.getPropertyValue(address, "zip_code");
			if (maybeZipCode.isPresent()) {
				addressString += addressString.isEmpty() ? "" : " ";
				// exclude zip code suffix so that google geocoding does not consider the address string as a partial match
				addressString += Splitter.on("-").splitToList(maybeZipCode.get()).get(0);
			}
		}

		Optional<String> maybeCountry = PropertyUtils.getPropertyValue(address, "country");
		if (maybeCountry.isPresent()) {
			addressString += addressString.isEmpty() ? "" : " ";
			addressString += maybeCountry.get();
		}

		return CharMatcher.ASCII.retainFrom(addressString);
	}

	public List<Row> checkGeocodeCache(Session geocodeKsSession, PreparedStatement selectFromCacheStmt,
			String addressString) {
		String addressHash = getCacheKey(addressString);
		LOG.info("Address string: " + addressString + ", md5: " + addressHash);
		ResultSet geocodeRs = geocodeKsSession.execute(selectFromCacheStmt.bind(addressHash));
		return geocodeRs.all();
	}

	public String getCacheKey(String addressString) {
		return Hashing.md5().newHasher().putString(addressString, Charsets.UTF_8).hash().toString();
	}

	public Map<String, Double> getGeocodeFromPayload(JsonNode json) {
		Map<String, Double> result = Maps.newHashMap();

		Iterable<JsonNode> results = () -> json.get("results").elements();
		for (JsonNode resultJson : results) {
			JsonNode location = resultJson.get("geometry").get("location");
			result.put("lat", location.get("lat").asDouble());
			result.put("lng", location.get("lng").asDouble());
			break;
		}

		return result;
	}

	public void addGeocodedAndCensusDataToAddress(NameValueObject.Builder addressBldr, Map<String, Double> geocode, CensusUtils censusUtils) {
		Double lat = geocode.get("lat");
		Double lng = geocode.get("lng");

		if (isValidGeocode(lat, lng)) {
			Map<String, Integer> censusData = censusUtils
					.getCensusData(PropertyUtils.getPropertyValue(addressBldr.build(), "state"), lat, lng);

			boolean privacy = derivePrivacy(addressBldr.build());
			addressBldr.clearNameValuePair();

			addPropertyToAddressBuilder(addressBldr, "lat", Optional.of(lat.toString()),
					UpdateSource.HADOOP, privacy, false);

			addPropertyToAddressBuilder(addressBldr, "lng", Optional.of(lng.toString()),
					UpdateSource.HADOOP, privacy, false);

			for (String censusItem : censusData.keySet()) {
				addPropertyToAddressBuilder(addressBldr, censusItem,
						Optional.of(String.valueOf(censusData.get(censusItem))), UpdateSource.HADOOP, privacy, false);
			}
		}
	}

	private boolean isValidGeocode(Double lat, Double lng) {
		return Range.closed(-90d, 90d).contains(lat) && Range.closed(-180d, 180d).contains(lng);
	}

	public boolean derivePrivacy(NameValueObject address) {
		for (NameValuePair nvp : address.getNameValuePairList()) {
			if (!GEOCODE_PROPERTIES.contains(nvp.getProperty().getName()) && nvp.getPrivate()) {
				return true;
			}
		}
		return false;
	}

	public Optional<UpdateSource> deriveOptionalPrivatizedSource(NameValueObject address) {
		for (NameValuePair nvp : address.getNameValuePairList()) {
			if (nvp.hasPrivatizedSource()) {
				return Optional.of(nvp.getPrivatizedSource());
			}
		}
		return Optional.absent();
	}

	public void addPropertyToAddressBuilder(NameValueObject.Builder addressBldr,
                                            String propName,
			                                Optional<String> maybeValue,
                                            UpdateSource updateSource,
                                            boolean privacy,
                                            boolean deleted) {
		if (maybeValue.isPresent()) {
			addressBldr.addNameValuePair(NameValuePair.newBuilder()
					.setProperty(Property.newBuilder().setName(propName).build()).setValue(maybeValue.get())
					.setUpdateSource(updateSource).setPrivate(privacy).setDeleted(deleted).build());
		}
	}
}
