package com.et.contacts.storm.utils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.ResultSetHandler;
import org.apache.log4j.Logger;

import com.et.contacts.storm.guice.GuiceSingleton;
import com.google.common.base.Optional;
import com.google.common.collect.Maps;

public class CensusUtils {
    private static final Logger LOG = Logger.getLogger (CensusUtils.class);
    private static final Map<String, String> STATE_LOOKUP_MAP = new HashMap<String, String> () {
        private static final long serialVersionUID = 3298925456037429160L;
        {
            put ("ALABAMA", "AL");
            put ("ALASKA", "AK");
            put ("ARIZONA", "AZ");
            put ("ARKANSAS", "AR");
            put ("CALIFORNIA", "CA");
            put ("COLORADO", "CO");
            put ("CONNECTICUT", "CT");
            put ("WASHINGTON DC", "DC");
            put ("DELAWARE", "DE");
            put ("FLORIDA", "FL");
            put ("GEORGIA", "GA");
            put ("HAWAII", "HI");
            put ("IDAHO", "ID");
            put ("ILLINOIS", "IL");
            put ("INDIANA", "IN");
            put ("IOWA", "IA");
            put ("KANSAS", "KS");
            put ("KENTUCKY", "KY");
            put ("LOUISIANA", "LA");
            put ("MAINE", "ME");
            put ("MARYLAND", "MD");
            put ("MASSACHUSETTS", "MA");
            put ("MICHIGAN", "MI");
            put ("MINNESOTA", "MN");
            put ("MISSISSIPPI", "MS");
            put ("MISSOURI", "MO");
            put ("MONTANA", "MT");
            put ("NEBRASKA", "NE");
            put ("NEVADA", "NV");
            put ("NEW HAMPSHIRE", "NH");
            put ("NEW JERSEY", "NJ");
            put ("NEW MEXICO", "NM");
            put ("NEW YORK", "NY");
            put ("NORTH CAROLINA", "NC");
            put ("NORTH DAKOTA", "ND");
            put ("OHIO", "OH");
            put ("OKLAHOMA", "OK");
            put ("OREGON", "OR");
            put ("PENNSYLVANIA", "PA");
            put ("RHODE ISLAND", "RI");
            put ("SOUTH CAROLINA", "SC");
            put ("SOUTH DAKOTA", "SD");
            put ("TENNESSEE", "TN");
            put ("TEXAS", "TX");
            put ("UTAH", "UT");
            put ("VERMONT", "VT");
            put ("VIRGINIA", "VA");
            put ("WASHINGTON", "WA");
            put ("WEST VIRGINIA", "WV");
            put ("WISCONSIN", "WI");
            put ("WYOMING", "WY");
        }
    };

    public Map<String, Integer> getCensusData (Optional<String> maybeState,
                                               double latitude,
                                               double longitude) {
        
        try {
            String state = null;
            
            if (maybeState.isPresent ()) {
                state = maybeState.get ();
            }
                        
            return getCensusData (state,
                                  latitude,
                                  longitude);
        } catch (Exception e) {
            return new HashMap<String, Integer> ();
        }
    }
    
    public Map<String, Integer> getCensusData (Optional<String> maybeState,
                                               Optional<String> maybeLatitude,
                                               Optional<String> maybeLongitude) {

        try {
            String state = null;

            if (maybeState.isPresent ()) {
                state = maybeState.get ();
            }

            double latitude  = Double.valueOf (maybeLatitude.get ());
            double longitude = Double.valueOf (maybeLongitude.get ());

            return getCensusData (state,
                                  latitude,
                                  longitude);
        } catch (Exception e) {
            return Maps.newHashMap ();
        }
    }

    public Map<String, Integer> getCensusData (String state,
                                               double latitude,
                                               double longitude) {
        Map<String, Integer> censusDataMap = Maps.newHashMap ();

        if (state != null) {
            if (state.length () > 2) {
                state = STATE_LOOKUP_MAP.get (state.toUpperCase ());
            }

            if (state != null) {
                if (!STATE_LOOKUP_MAP.containsValue (state.toUpperCase ())) {
                    state = null;
                } else {
                    state = "STATE_" + state.toUpperCase ();
                }
            }
        }

        // We are no longer doing lookups for invalid states, as this takes way too long.
//        if (state == null) {
//            state = "UNITED_STATES"; // Force to do a slow lookup in the table containing all the data for all states.
//        }

        try {
			QueryRunner censusQueryRunner = GuiceSingleton.get().getInstanceWithName(QueryRunner.class,
                                                                                     "CensusQueryRunner");

            censusDataMap.putAll (censusQueryRunner.query ("SELECT B25076_001E, B25077_001E, B25078_001E, B19013_001E FROM " + state + " WHERE ST_Contains(geom, ST_GeomFromText('POINT(" + longitude + " " + latitude + ")'))",
                                                           new ResultSetHandler<Map<String, Integer>> () {
                @Override
                public Map<String, Integer> handle(ResultSet rs) throws SQLException {
                    Map<String, Integer> result = Maps.newHashMap ();

                    if (rs.next ()) {
                        int houseValueLq     = rs.getInt ("B25076_001E");
                        if (houseValueLq > 0) {
                            result.put ("house_value_lq", houseValueLq);
                        }

                        int houseValueMedian = rs.getInt ("B25077_001E");
                        if (houseValueMedian > 0) {
                            result.put ("house_value_median", houseValueMedian);
                        }

                        int houseValueUq     = rs.getInt ("B25078_001E");
                        if (houseValueUq > 0) {
                            result.put ("house_value_uq", houseValueUq);
                        }

                        int incomeMedian     = rs.getInt ("B19013_001E");
                        if (incomeMedian > 0) {
                            result.put ("income_median", incomeMedian);
                        }
                    }

                    rs.close ();

                    return result;
                }}));
        } catch (SQLException e) {
            censusDataMap.clear ();
        }

        return censusDataMap;
    }
}
