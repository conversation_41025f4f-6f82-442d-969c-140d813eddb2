package com.et.contacts.storm.topology;

import com.et.contacts.storm.bolt.GeocodeChangeWithGoogleBolt;
import com.et.contacts.storm.bolt.GeocodeUpdateHandlingPrivAndDelBolt;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.storm.spout.ContactMultiScheme;
import com.google.common.base.Strings;

import backtype.storm.Config;
import backtype.storm.LocalCluster;
import backtype.storm.StormSubmitter;
import backtype.storm.topology.TopologyBuilder;
import backtype.storm.tuple.Fields;
import storm.kafka.KafkaSpout;
import storm.kafka.SpoutConfig;
import storm.kafka.ZkHosts;

public class ContactGeocodeTopology {

	public static void main(String[] args) throws Exception {
		String topologyName = args.length > 0 ? args[0] : "contact-geocode";

		TopologyBuilder builder = new TopologyBuilder();
		int geocodeConcurrency = getGeocodeConcurrency(args);

		String kafkaZkConnect = GuiceSingleton.get().getInstanceWithName(String.class,
				"com.et.contacts.kafka.zookeeper.connect");
		String kafkaContactsGeocodeTopic = GuiceSingleton.get().getInstanceWithName(String.class,
                "com.et.contacts.kafka.topic.contacts_geocode");

		ZkHosts zkHosts = new ZkHosts(kafkaZkConnect);

        String geocodeKafkaSpoutName = "geocode-kafka-spout";
        SpoutConfig geocodeSpoutConfig = new SpoutConfig(zkHosts, kafkaContactsGeocodeTopic, "/storm-kafka/"+topologyName, geocodeKafkaSpoutName);
        geocodeSpoutConfig.scheme = new ContactMultiScheme();
        geocodeSpoutConfig.retryInitialDelayMs = 1000;
        geocodeSpoutConfig.retryDelayMultiplier = 2;
        geocodeSpoutConfig.retryDelayMaxMs = 12 * 60 * 60 * 1000;
        KafkaSpout geocodeKafkaSpout = new KafkaSpout(geocodeSpoutConfig);

        builder.setSpout(geocodeKafkaSpoutName, geocodeKafkaSpout, 6);

        builder.setBolt("geocode-change", new GeocodeChangeWithGoogleBolt(), 6)
                .fieldsGrouping(geocodeKafkaSpoutName, new Fields("contact_id"));
        builder.setBolt("geocode-update", new GeocodeUpdateHandlingPrivAndDelBolt(), 6)
                .fieldsGrouping("geocode-change", new Fields("contact_id"));

		Config conf = new Config();
		conf.setDebug(false);
		conf.setMaxSpoutPending(1);
		conf.setMessageTimeoutSecs(60);
		conf.setStatsSampleRate(1.0);

		if (args != null && args.length > 0) {
			conf.setNumWorkers(1);

			StormSubmitter.submitTopology(topologyName, conf, builder.createTopology());
		} else {
			conf.setMaxTaskParallelism(30);
			conf.setMessageTimeoutSecs(30);

			LocalCluster cluster = new LocalCluster();
			cluster.submitTopology(topologyName, conf, builder.createTopology());

			Thread.sleep(5000000);

			cluster.shutdown();
		}
	}

	private static int getGeocodeConcurrency(String[] args) {
		if (args.length < 1 || Strings.isNullOrEmpty(args[1])) {
			return 3;
		}

		return Integer.valueOf(args[1]);
	}
}
