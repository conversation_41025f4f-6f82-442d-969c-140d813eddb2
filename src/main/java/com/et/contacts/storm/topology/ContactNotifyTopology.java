package com.et.contacts.storm.topology;

import backtype.storm.Config;
import backtype.storm.LocalCluster;
import backtype.storm.StormSubmitter;
import backtype.storm.topology.TopologyBuilder;
import backtype.storm.tuple.Fields;
import com.et.contacts.storm.bolt.CassandraSyncBolt;
import com.et.contacts.storm.bolt.ElasticSearchActivityIndexBolt;
import com.et.contacts.storm.bolt.ElasticSearchGiftBolt;
import com.et.contacts.storm.bolt.ElasticSearchIndexBolt;
import com.et.contacts.storm.bolt.GeocodeDeleteBolt;
import com.et.contacts.storm.bolt.GeocodeEnqueueBolt;
import com.et.contacts.storm.bolt.GeocodePrivatizeBolt;
import com.et.contacts.storm.bolt.GeocodeUpdateHandlingDelBolt;
import com.et.contacts.storm.bolt.GeocodeUpdateHandlingPrivAndDelBolt;
import com.et.contacts.storm.bolt.GeocodeUpdateHandlingPrivBolt;
import com.et.contacts.storm.bolt.GiftDeleteBolt;
import com.et.contacts.storm.bolt.LidsNotificationBolt;
import com.et.contacts.storm.bolt.SendGridBolt;
import com.et.contacts.storm.bolt.SuggestedUpdateIndexingBolt;
import com.et.contacts.storm.bolt.UpdateEventEngagementsForEmailBolt;
import com.et.contacts.storm.guice.GuiceSingleton;
import com.et.contacts.storm.spout.ContactChangeEventMultiScheme;
import com.et.contacts.storm.spout.EmailSpout;
import com.et.contacts.storm.spout.SuggestedUpdateSpout;
import com.google.common.base.Strings;
import storm.kafka.KafkaSpout;
import storm.kafka.SpoutConfig;
import storm.kafka.ZkHosts;

public class ContactNotifyTopology {
    private static final String EVENTS_URL = "com.et.events.base_url";

	public static void main(String[] args) throws Exception {
		String topologyName = args.length > 0 ? args[0] : "contact-change";

		TopologyBuilder builder = new TopologyBuilder();
		int esConcurrency = getEsIndexConcurrency(args);
		int geocodeConcurrency = getGeocodeConcurrency(args);
		int lidsConcurrency = getLidsChangeConcurrency(args);
		int giftConcurrency = getGiftConcurrency(args);

		String emailQueue = GuiceSingleton.get().getInstanceWithName(String.class,
				"com.et.contacts.email.sqs.queue.name");
		String suggestedUpdateQueue = GuiceSingleton.get().getInstanceWithName(String.class,
				"com.et.contacts.suggested_update.sqs.queue.name");
		String kafkaZkConnect = GuiceSingleton.get().getInstanceWithName(String.class,
				"com.et.contacts.kafka.zookeeper.connect");
		String kafkaContactsWalTopic = GuiceSingleton.get().getInstanceWithName(String.class,
				"com.et.contacts.kafka.topic.contacts_wal");
		String kafkaContactsRefreshTopic = GuiceSingleton.get().getInstanceWithName(String.class,
				"com.et.contacts.kafka.topic.contacts_refresh");

		builder.setSpout("email-spout", new EmailSpout(emailQueue), 1);
		builder.setSpout("suggested-update-spout", new SuggestedUpdateSpout(suggestedUpdateQueue), 1);

		ZkHosts zkHosts = new ZkHosts(kafkaZkConnect);

		String changeKafkaSpoutName = "change-kafka-spout";
		SpoutConfig changeSpoutConfig = new SpoutConfig(zkHosts, kafkaContactsWalTopic, "/storm-kafka/"+topologyName, changeKafkaSpoutName);
		changeSpoutConfig.scheme = new ContactChangeEventMultiScheme();
		KafkaSpout changeKafkaSpout = new KafkaSpout(changeSpoutConfig);

		String refreshKafkaSpoutName = "refresh-kafka-spout";
		SpoutConfig refreshSpoutConfig = new SpoutConfig(zkHosts, kafkaContactsRefreshTopic, "/storm-kafka/"+topologyName, refreshKafkaSpoutName);
		refreshSpoutConfig.scheme = new ContactChangeEventMultiScheme();
		KafkaSpout refreshKafkaSpout = new KafkaSpout(refreshSpoutConfig);

		String esSyncSpoutName = "es-sync-kafka-spout";
		SpoutConfig esSyncSpoutConfig = new SpoutConfig(zkHosts, kafkaContactsWalTopic, "/storm-kafka/"+topologyName, esSyncSpoutName);
		esSyncSpoutConfig.scheme = new ContactChangeEventMultiScheme();
		KafkaSpout esSyncSpout = new KafkaSpout(esSyncSpoutConfig);

		String giftKafkaSpoutName = "gift-kafka-spout";
		SpoutConfig giftKafkaSpoutConfig = new SpoutConfig(zkHosts, kafkaContactsWalTopic, "/storm-kafka/"+topologyName, giftKafkaSpoutName);
		giftKafkaSpoutConfig.scheme = new ContactChangeEventMultiScheme();
		KafkaSpout giftKafkaSpout = new KafkaSpout((giftKafkaSpoutConfig));

		String geocodeKafkaSpoutName = "geocode-kafka-spout";
		SpoutConfig geocodeKafkaSpoutConfig = new SpoutConfig(zkHosts, kafkaContactsWalTopic, "/storm-kafka/"+topologyName, geocodeKafkaSpoutName);
		geocodeKafkaSpoutConfig.scheme = new ContactChangeEventMultiScheme();
		KafkaSpout geocodeKafkaSpout = new KafkaSpout(geocodeKafkaSpoutConfig);

		builder.setSpout(changeKafkaSpoutName, changeKafkaSpout, 3);
		builder.setSpout(refreshKafkaSpoutName, refreshKafkaSpout, 3);

		builder.setSpout(esSyncSpoutName, esSyncSpout, 3);
		builder.setSpout(giftKafkaSpoutName, giftKafkaSpout, 3);
		builder.setSpout(geocodeKafkaSpoutName, geocodeKafkaSpout, 3);

		// the original change spout
		builder.setBolt("lids-change-notify", new LidsNotificationBolt(), lidsConcurrency).shuffleGrouping(changeKafkaSpoutName);

		builder.setBolt("cassandra-sync", new CassandraSyncBolt(), esConcurrency)
				.fieldsGrouping(changeKafkaSpoutName, new Fields("contact_id"))
				.fieldsGrouping(refreshKafkaSpoutName, new Fields("contact_id"));

		builder.setBolt("es-activity", new ElasticSearchActivityIndexBolt(), esConcurrency)
				.fieldsGrouping(changeKafkaSpoutName, new Fields("contact_id"));

		String eventsUrl = GuiceSingleton.get().getInstanceWithName(String.class, EVENTS_URL);
		builder.setBolt("update-event-engagements-for-email-bolt", new UpdateEventEngagementsForEmailBolt(eventsUrl), esConcurrency)
				.fieldsGrouping(changeKafkaSpoutName, new Fields("contact_id"));

		// es sync spout
		builder.setBolt("es-index", new ElasticSearchIndexBolt(), esConcurrency)
				.fieldsGrouping(esSyncSpoutName, new Fields("contact_id"))
				.fieldsGrouping(refreshKafkaSpoutName, new Fields("contact_id"));

		// gift spout
		builder.setBolt("gift-delete", new GiftDeleteBolt(), giftConcurrency).fieldsGrouping(
				giftKafkaSpoutName, new Fields("contact_id"));
		builder.setBolt("elastic-search-gift", new ElasticSearchGiftBolt(), giftConcurrency)
				.fieldsGrouping(giftKafkaSpoutName, new Fields("contact_id"));

		// geocode spout
		builder.setBolt("geocode-enqueue", new GeocodeEnqueueBolt(), geocodeConcurrency)
				.fieldsGrouping(geocodeKafkaSpoutName, new Fields("contact_id"));
		builder.setBolt("geocode-update-cache", new GeocodeUpdateHandlingPrivAndDelBolt(), geocodeConcurrency)
				.fieldsGrouping("geocode-enqueue", new Fields("contact_id"));
		builder.setBolt("geocode-privatize", new GeocodePrivatizeBolt(), 3)
				.fieldsGrouping(geocodeKafkaSpoutName, new Fields("contact_id"));
		builder.setBolt("geocode-update-privatize", new GeocodeUpdateHandlingPrivBolt(), 3)
				.fieldsGrouping("geocode-privatize", new Fields("contact_id"));
		builder.setBolt("geocode-delete", new GeocodeDeleteBolt(), 3)
				.fieldsGrouping(geocodeKafkaSpoutName, new Fields("contact_id"));
		builder.setBolt("geocode-update-delete", new GeocodeUpdateHandlingDelBolt(), 3)
				.fieldsGrouping("geocode-delete", new Fields("contact_id"));

		// misc
		builder.setBolt("sendgrid-bolt", new SendGridBolt(), 1).fieldsGrouping("email-spout",
				new Fields("unique_msg_id"));

		builder.setBolt("suggested-update-bolt", new SuggestedUpdateIndexingBolt(), 1).shuffleGrouping(
				"suggested-update-spout");

		Config conf = new Config();
		conf.setDebug(false);
		conf.setMaxSpoutPending(10);
		conf.setMessageTimeoutSecs(60);
		conf.setStatsSampleRate(1.0);

		if (args != null && args.length > 0) {
			conf.setNumWorkers(1);

			StormSubmitter.submitTopology(topologyName, conf, builder.createTopology());
		} else {
			conf.setMaxTaskParallelism(30);
			conf.setMessageTimeoutSecs(60);

			LocalCluster cluster = new LocalCluster();
			cluster.submitTopology(topologyName, conf, builder.createTopology());

			Thread.sleep(5000000);

			cluster.shutdown();
		}
	}

	private static int getEsIndexConcurrency(String[] args) {
		if (args.length < 1 || Strings.isNullOrEmpty(args[1])) {
			return 50;
		}

		return Integer.valueOf(args[1]);
	}

	private static int getGeocodeConcurrency(String[] args) {
		if (args.length < 3 || Strings.isNullOrEmpty(args[3])) {
			return 10;
		}

		return Integer.valueOf(args[3]);
	}

	private static int getLidsChangeConcurrency(String[] args) {
		if (args.length < 4 || Strings.isNullOrEmpty(args[4])) {
			return 10;
		}

		return Integer.valueOf(args[4]);
	}

	private static int getGiftConcurrency(String[] args) {
		if (args.length < 5 || Strings.isNullOrEmpty(args[5])) {
			return 10;
		}

		return Integer.valueOf(args[5]);
	}
}
