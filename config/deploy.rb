set :topology_name, ask('which topology to deploy?', 'contact-change')
set :application, "contacts_storm-#{fetch(:topology_name)}"
set :branch, ENV['BRANCH'] || 'master'

set :user, 'deploy'
set :deploy_to, "/home/<USER>/contacts_storm"

set :deployed_artifact_filename, 'contacts_storm-0.0.1-SNAPSHOT-shaded.jar'

# S3 Configuration
set :s3_bucket, 'codebuild.evertrue.com'
set :s3_prefix, 'builds/contacts_storm'
set :aws_region, 'us-east-1'
set :timestamp, Time.now.strftime("%Y%m%d%H%M%S")
set :release_path, Pathname.new("#{fetch(:deploy_to)}/releases/#{fetch(:timestamp)}")

topology_args = {
  'contact-change' => {
    topology_class_name: 'com.et.contacts.storm.topology.ContactNotifyTopology',
    topology_other_args: {
      staging: '5 4 4 5 5',
      production: '30 8 20 10 10'
    }
  },
  'contact-geocode' => {
    topology_class_name: 'com.et.contacts.storm.topology.ContactGeocodeTopology',
    topology_other_args: {
      staging: '1',
      production: '3'
    }
  }
}

fail 'Invalid topology name' unless topology_args.key?(fetch(:topology_name))

set :topology_class_name, -> { topology_args[fetch(:topology_name)][:topology_class_name] }
set :topology_other_args, -> { topology_args[fetch(:topology_name)][:topology_other_args][fetch(:stage)] }

set :slack_team, 'evertrue'
set :slack_token, 'nr60DXdozeC9ouL177mjVpp7'
set :slack_channel, -> { '#deployments' }
