# Load DSL and Setup Up Stages
require 'capistrano/setup'

# Includes default deployment tasks
require 'capistrano/deploy'

# Include Storm-specific deployment tasks
require 'capistrano/storm'

# Notify Slack of deployments
require 'slackistrano'

require 'stringio'

# Add lib to load path
$LOAD_PATH << 'lib'

# Load the S3 SCM plugin
require 'capistrano/s3'

# Load custom tasks from `lib/capistrano/tasks` if you have any defined
Dir.glob('lib/capistrano/tasks/*.rake').each { |r| import r }
