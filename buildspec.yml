version: 0.2

env:
  variables:
    region: us-east-1
    registry_id: "478370645242"
    project_name: contacts_storm
    s3_path: "s3://codebuild.evertrue.com/builds/contacts_storm"
    contacts_storm_jar: "target/contacts_storm-0.0.1-SNAPSHOT-shaded.jar"

phases:
  install:
    runtime-versions:
      java: corretto8

    commands:
      - echo Installing codebuild-extras...
      - curl -fsSL https://raw.githubusercontent.com/evertrue/aws-codebuild-extras/master/install >> extras.sh
      - . ./extras.sh

  pre_build:
    commands:
      - aws s3 cp s3://codebuild.evertrue.com/.m2/settings.xml /root/.m2/settings.xml
      - aws ecr get-login-password --region $region | docker login --username AWS --password-stdin $registry_id.dkr.ecr.$region.amazonaws.com

  build:
    commands:
      - mvn -U clean verify
      - aws s3 cp ${contacts_storm_jar} ${s3_path}/${CODEBUILD_GIT_BRANCH}/

cache:
  paths:
    - '/root/.m2/**/*'
