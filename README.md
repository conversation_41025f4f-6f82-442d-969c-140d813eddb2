# Contacts Storm Topologies

![Storm](http://3.bp.blogspot.com/-qLmVe4y3Ebg/T2TKRgRrDBI/AAAAAAAAEAw/PAIYAe1ncUM/s1600/halle+2.jpg)

This project contains the following topologies:

* [Contact Notifications](#contact-notifications)
* [Geocoding](#geocoding)
* [Generic Emailing](#generic-emailing)

## Deployment

Capistrano is used to handle deploying the topology from Jenkins to the Storm Nimbus node.

### Requirements

* Ruby >= 1.9
* Bundler

### Getting Started

1. Clone the project, and `cd` into the newly created directory.
2. Run `bundle` to install the necessary gems
3. Use `bundle exec cap [staging|production] deploy` to ship the most recently built topology from Jenkins to the Storm Nimbus node
    * Cap will ask you which topology you’d like to ship, defaulting to `contact-change` (i.e., [Contact Notifications](#contact-notifications)). The topologies that you can ship are: contact-change, contact-geocode
    * Deploy brach with `bundle exec cap [staging|production] deploy BRANCH=[branch name]`
4. Profit

### View storm logs
 * Storm UI: `http://[storm-nimbus-server]:[port]/index.html` contains lists of topologies, can click through, then find Spouts or Bolts
 * Drilling down to the specific sprout or bolt of interest view `Host` under the `Executors` section.
 * SSH into the host machine and `cd /mnt/storm/logs`
 * view log of worker ending with the `port` seen in the Storm UI next to the `Host` under the `Executors` section.

## Contact Notifications

TODO: Add docs

## Geocoding

Quick link for geocoding tester: https://google-developers.appspot.com/maps/documentation/utils/geocoder/
