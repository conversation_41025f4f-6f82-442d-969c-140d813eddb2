namespace :s3 do
  desc 'Create a new release'
  task :create_release do
    on roles(:app) do
      # Create a timestamped release directory
      execute :mkdir, '-p', fetch(:release_path)

      # Construct S3 object key
      object_key = "#{fetch(:s3_prefix)}/#{fetch(:branch)}/#{fetch(:deployed_artifact_filename)}"
      local_file = File.join(fetch(:release_path), fetch(:deployed_artifact_filename))
      
      # Download the file directly from S3 to the remote server
      execute :aws, "s3", "cp", "--region", fetch(:aws_region),
             "s3://#{fetch(:s3_bucket)}/#{object_key}", local_file
      
      # Verify the file was downloaded
      unless test("[ -f #{local_file} ]")
        error "Failed to download #{fetch(:deployed_artifact_filename)} from S3"
        exit 1
      end
      
      # Verify the file is not empty
      unless test("[ -s #{local_file} ]")
        error "Downloaded file #{fetch(:deployed_artifact_filename)} is empty"
        exit 1
      end
      
      info "Successfully downloaded #{local_file} from S3"
    end
  end

  desc 'Set the current revision'
  task :set_current_revision do
    set :current_revision, fetch(:branch)
  end
end 
