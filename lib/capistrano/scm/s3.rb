require 'capistrano/scm/plugin'

module Capistrano
  class SCM
    class S3 < Plugin
      def set_defaults
      end

      def register_hooks
        # Remove all default hooks
        Rake::Task['deploy:new_release_path'].clear_actions
        Rake::Task['deploy:set_current_revision'].clear_actions
        Rake::Task['deploy:updating'].clear_actions
        Rake::Task['deploy:updated'].clear_actions
        
        # Add our custom hooks
        after 'deploy:started', 's3:create_release'
        before 'deploy:set_current_revision', 's3:set_current_revision'
      end

      def define_tasks
        eval_rakefile File.expand_path('../../tasks/s3.rake', __FILE__)
      end

      def fetch_revision
        fetch(:branch)
      end

      # Override the default create_release method to prevent double execution
      def create_release
        # This is intentionally empty as we handle the release creation in our custom task
      end
    end
  end
end 
